from django.core.management.base import BaseCommand
from eeucontractors.models import Vendorsregistration, Vendor


class Command(BaseCommand):
    help = 'Create Vendor records for existing Vendorsregistration records'

    def add_arguments(self, parser):
        parser.add_argument(
            '--dry-run',
            action='store_true',
            help='Show what would be created without actually creating records',
        )

    def handle(self, *args, **options):
        dry_run = options['dry_run']
        
        # Get all Vendorsregistration records that don't have a corresponding Vendor
        registrations_without_vendor = Vendorsregistration.objects.filter(
            vendor__isnull=True
        )
        
        total_count = registrations_without_vendor.count()
        
        if total_count == 0:
            self.stdout.write(
                self.style.SUCCESS('✅ All Vendorsregistration records already have corresponding Vendor records!')
            )
            return
        
        self.stdout.write(f'Found {total_count} Vendorsregistration records without Vendor records')
        
        if dry_run:
            self.stdout.write(self.style.WARNING('DRY RUN - No records will be created'))
            for reg in registrations_without_vendor:
                self.stdout.write(f'  Would create Vendor for: {reg.contractor_name} (ID: {reg.id})')
            return
        
        created_count = 0
        error_count = 0
        
        for registration in registrations_without_vendor:
            try:
                vendor = Vendor.objects.create(vendor_registration=registration)
                created_count += 1
                self.stdout.write(f'✅ Created Vendor for: {registration.contractor_name}')
            except Exception as e:
                error_count += 1
                self.stdout.write(
                    self.style.ERROR(f'❌ Error creating Vendor for {registration.contractor_name}: {e}')
                )
        
        self.stdout.write(
            self.style.SUCCESS(
                f'\n🎉 Sync completed!\n'
                f'  Created: {created_count} Vendor records\n'
                f'  Errors: {error_count}\n'
                f'  Total processed: {total_count}'
            )
        )
