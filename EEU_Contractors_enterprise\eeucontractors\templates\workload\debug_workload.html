{% extends 'base.html' %}

{% block content %}
<div class="container mt-4">
  <h2>🐛 Workload Creation Debug</h2>
  
  <div class="row">
    <div class="col-md-6">
      <div class="card">
        <div class="card-header bg-info text-white">
          <h5>Project Information</h5>
        </div>
        <div class="card-body">
          <p><strong>Project:</strong> {{ project_progress.project.project_name }}</p>
          <p><strong>Vendor:</strong> {{ project_progress.vendor.name }}</p>
          <p><strong>Main Activity:</strong> {{ project_progress.main_activity.name }}</p>
          <p><strong>Progress ID:</strong> {{ project_progress.id }}</p>
        </div>
      </div>
    </div>
    
    <div class="col-md-6">
      <div class="card">
        <div class="card-header bg-warning text-dark">
          <h5>Completion Status</h5>
        </div>
        <div class="card-body">
          <p><strong>Total Expected:</strong> {{ completion_status.total_expected }}</p>
          <p><strong>Total Assigned:</strong> {{ completion_status.total_assigned }}</p>
          <p><strong>Missing Workloads:</strong> {{ completion_status.missing_workloads }}</p>
          <p><strong>Has Missing:</strong> {{ completion_status.has_missing_workloads }}</p>
          <p><strong>Completion %:</strong> {{ completion_status.completion_percentage }}%</p>
        </div>
      </div>
    </div>
  </div>
  
  <div class="row mt-4">
    <div class="col-md-6">
      <div class="card">
        <div class="card-header bg-primary text-white">
          <h5>Expected Sub-Activities</h5>
        </div>
        <div class="card-body">
          {% for sub in expected_sub_activities %}
            <div class="border-bottom pb-2 mb-2">
              <strong>{{ sub.name }}</strong><br>
              <small>Base Duration: {{ sub.base_duration }} days</small><br>
              <small>Order: {{ sub.order }}</small>
            </div>
          {% empty %}
            <p class="text-muted">No sub-activities found</p>
          {% endfor %}
        </div>
      </div>
    </div>
    
    <div class="col-md-6">
      <div class="card">
        <div class="card-header bg-success text-white">
          <h5>Existing Workloads</h5>
        </div>
        <div class="card-body">
          {% for workload in existing_workloads %}
            <div class="border-bottom pb-2 mb-2">
              <strong>{{ workload.sub_activity.name }}</strong><br>
              <small>Quantity: {{ workload.quantity }}</small><br>
              <small>Expected Duration: {{ workload.expected_duration }} days</small><br>
              <small>Progress: {{ workload.quantity_progress_percentage }}%</small>
            </div>
          {% empty %}
            <p class="text-muted">No workloads found</p>
          {% endfor %}
        </div>
      </div>
    </div>
  </div>
  
  <div class="row mt-4">
    <div class="col-12">
      <div class="card">
        <div class="card-header bg-danger text-white">
          <h5>Test Auto-Create</h5>
        </div>
        <div class="card-body">
          {% if test_result %}
            <div class="alert alert-success">{{ test_result }}</div>
          {% endif %}
          
          {% if test_error %}
            <div class="alert alert-danger">Error: {{ test_error }}</div>
          {% endif %}
          
          <form method="get" class="d-inline-flex align-items-center gap-2">
            <label for="quantity">Test Quantity:</label>
            <input type="number" name="quantity" value="5" min="1" max="100" class="form-control" style="width: 100px;">
            <input type="hidden" name="test_create" value="true">
            <button type="submit" class="btn btn-danger">Test Create Workloads</button>
          </form>
          
          <hr>
          <a href="{% url 'workload_list' progress_id=project_progress.id %}" class="btn btn-primary">
            Back to Workload List
          </a>
        </div>
      </div>
    </div>
  </div>
</div>
{% endblock %}
