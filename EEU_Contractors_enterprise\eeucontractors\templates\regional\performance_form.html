{% extends "base.html" %}
{% load static %}

{% block content %}
<div class="container py-4">
  <div class="row justify-content-center">
    <div class="col-lg-10">
      <div class="card shadow-sm">
        <div class="card-header bg-primary text-white">
          <h5 class="mb-0">
            <i class="bi bi-graph-up me-2"></i>{{ title }}
          </h5>
        </div>
        
        <div class="card-body">
          <div class="alert alert-info">
            <i class="bi bi-info-circle me-2"></i>
            <strong>Note:</strong> Most fields are automatically calculated and read-only. 
            Statistics are updated automatically when you save.
          </div>
          
          <form method="post">
            {% csrf_token %}
            
            {% if form.non_field_errors %}
              <div class="alert alert-danger">
                {{ form.non_field_errors }}
              </div>
            {% endif %}
            
            <!-- Region Information -->
            <div class="card mb-4">
              <div class="card-header bg-light">
                <h6 class="mb-0">Region Information</h6>
              </div>
              <div class="card-body">
                <div class="mb-3">
                  <label for="{{ form.region.id_for_label }}" class="form-label">
                    {{ form.region.label }}
                  </label>
                  {{ form.region }}
                  {% if form.region.errors %}
                    <div class="text-danger small">{{ form.region.errors }}</div>
                  {% endif %}
                </div>
              </div>
            </div>
            
            <!-- Project Statistics -->
            <div class="card mb-4">
              <div class="card-header bg-light">
                <h6 class="mb-0">Project Statistics</h6>
              </div>
              <div class="card-body">
                <div class="row">
                  <div class="col-md-3 mb-3">
                    <label for="{{ form.total_projects.id_for_label }}" class="form-label">
                      {{ form.total_projects.label }}
                    </label>
                    {{ form.total_projects }}
                  </div>
                  
                  <div class="col-md-3 mb-3">
                    <label for="{{ form.completed_projects.id_for_label }}" class="form-label">
                      {{ form.completed_projects.label }}
                    </label>
                    {{ form.completed_projects }}
                  </div>
                  
                  <div class="col-md-3 mb-3">
                    <label for="{{ form.active_projects.id_for_label }}" class="form-label">
                      {{ form.active_projects.label }}
                    </label>
                    {{ form.active_projects }}
                  </div>
                  
                  <div class="col-md-3 mb-3">
                    <label for="{{ form.delayed_projects.id_for_label }}" class="form-label">
                      {{ form.delayed_projects.label }}
                    </label>
                    {{ form.delayed_projects }}
                  </div>
                </div>
              </div>
            </div>
            
            <!-- Vendor Statistics -->
            <div class="card mb-4">
              <div class="card-header bg-light">
                <h6 class="mb-0">Vendor Statistics</h6>
              </div>
              <div class="card-body">
                <div class="row">
                  <div class="col-md-4 mb-3">
                    <label for="{{ form.total_vendors.id_for_label }}" class="form-label">
                      {{ form.total_vendors.label }}
                    </label>
                    {{ form.total_vendors }}
                  </div>
                  
                  <div class="col-md-4 mb-3">
                    <label for="{{ form.active_vendors.id_for_label }}" class="form-label">
                      {{ form.active_vendors.label }}
                    </label>
                    {{ form.active_vendors }}
                  </div>
                  
                  <div class="col-md-4 mb-3">
                    <label for="{{ form.blacklisted_vendors.id_for_label }}" class="form-label">
                      {{ form.blacklisted_vendors.label }}
                    </label>
                    {{ form.blacklisted_vendors }}
                  </div>
                </div>
              </div>
            </div>
            
            <!-- Performance Metrics -->
            <div class="card mb-4">
              <div class="card-header bg-light">
                <h6 class="mb-0">Performance Metrics</h6>
              </div>
              <div class="card-body">
                <div class="row">
                  <div class="col-md-6 mb-3">
                    <label for="{{ form.average_project_completion_time.id_for_label }}" class="form-label">
                      {{ form.average_project_completion_time.label }}
                    </label>
                    {{ form.average_project_completion_time }}
                    <small class="form-text text-muted">Average days to complete projects</small>
                  </div>
                  
                  <div class="col-md-6 mb-3">
                    <label for="{{ form.average_progress_percentage.id_for_label }}" class="form-label">
                      {{ form.average_progress_percentage.label }}
                    </label>
                    {{ form.average_progress_percentage }}
                    <small class="form-text text-muted">Average progress across all active projects</small>
                  </div>
                </div>
              </div>
            </div>
            
            <!-- Financial Metrics -->
            <div class="card mb-4">
              <div class="card-header bg-light">
                <h6 class="mb-0">Financial Metrics</h6>
              </div>
              <div class="card-body">
                <div class="row">
                  <div class="col-md-6 mb-3">
                    <label for="{{ form.total_project_value.id_for_label }}" class="form-label">
                      {{ form.total_project_value.label }}
                    </label>
                    {{ form.total_project_value }}
                    <small class="form-text text-muted">Total value of all projects</small>
                  </div>
                  
                  <div class="col-md-6 mb-3">
                    <label for="{{ form.completed_project_value.id_for_label }}" class="form-label">
                      {{ form.completed_project_value.label }}
                    </label>
                    {{ form.completed_project_value }}
                    <small class="form-text text-muted">Value of completed projects</small>
                  </div>
                </div>
              </div>
            </div>
            
            <!-- Form Actions -->
            <div class="d-flex justify-content-between">
              <a href="{% url 'regional_performance_list' %}" class="btn btn-secondary">
                <i class="bi bi-arrow-left me-1"></i>Back to List
              </a>
              <button type="submit" class="btn btn-primary">
                <i class="bi bi-check-circle me-1"></i>Update Performance
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>
  </div>
</div>
{% endblock %}
