{% extends "base.html" %}
{% load static %}

{% block title %}{{ title }}{% endblock %}

{% block content %}
<style>
  /* Custom styling for document form */
  .card {
    border: 1px solid #ddd;
    transition: border-color 0.3s ease;
  }

  input.form-control, select.form-control, textarea.form-control {
    border: 1px solid #ccc;
    transition: border-color 0.3s ease;
  }

  input.form-control:hover, select.form-control:hover, textarea.form-control:hover,
  input.form-control:focus, select.form-control:focus, textarea.form-control:focus {
    border-color: #007bff;
    box-shadow: 0 0 5px rgba(0, 123, 255, 0.3);
    outline: none;
  }

  .file-upload-info {
    background-color: #f8f9fa;
    border: 1px dashed #dee2e6;
    border-radius: 0.375rem;
    padding: 1rem;
    text-align: center;
  }
</style>

<div class="container mt-4" style="max-width: 900px;">
  <div class="card shadow-sm rounded-3 p-4">
    <h3 class="mb-4"><i class="bi bi-file-earmark-plus me-2"></i>{{ title }}</h3>

    {% if messages %}
      <div class="messages mb-4">
        {% for message in messages %}
          <div class="alert alert-{{ message.tags }}">{{ message }}</div>
        {% endfor %}
      </div>
    {% endif %}

    <!-- Display non-field errors -->
    {% if form.non_field_errors %}
      <div class="alert alert-danger">
        <strong>Error:</strong>
        {% for error in form.non_field_errors %}
          <div>{{ error }}</div>
        {% endfor %}
      </div>
    {% endif %}

    <form method="post" enctype="multipart/form-data" novalidate>
      {% csrf_token %}

      <div class="row">
        <!-- Vendor Selection -->
        <div class="col-md-6 mb-3">
          <label for="{{ form.vendor.id_for_label }}" class="form-label fw-semibold">
            {{ form.vendor.label }}
            {% if form.vendor.field.required %}
              <span class="text-danger">*</span>
            {% endif %}
          </label>
          {{ form.vendor }}
          {% if form.vendor.help_text %}
            <small class="form-text text-muted">{{ form.vendor.help_text }}</small>
          {% endif %}
          {% if form.vendor.errors %}
            <div class="text-danger small mt-1">
              {% for error in form.vendor.errors %}{{ error }}{% endfor %}
            </div>
          {% endif %}
        </div>

        <!-- Document Type -->
        <div class="col-md-6 mb-3">
          <label for="{{ form.document_type.id_for_label }}" class="form-label fw-semibold">
            {{ form.document_type.label }}
            {% if form.document_type.field.required %}
              <span class="text-danger">*</span>
            {% endif %}
          </label>
          {{ form.document_type }}
          {% if form.document_type.help_text %}
            <small class="form-text text-muted">{{ form.document_type.help_text }}</small>
          {% endif %}
          {% if form.document_type.errors %}
            <div class="text-danger small mt-1">
              {% for error in form.document_type.errors %}{{ error }}{% endfor %}
            </div>
          {% endif %}
        </div>

        <!-- File Upload -->
        <div class="col-md-12 mb-3">
          <label for="{{ form.file.id_for_label }}" class="form-label fw-semibold">
            {{ form.file.label }}
            {% if form.file.field.required %}
              <span class="text-danger">*</span>
            {% endif %}
          </label>
          <div class="file-upload-info mb-2">
            <i class="bi bi-cloud-upload fs-2 text-muted"></i>
            <p class="mb-1">Select a file to upload</p>
            <small class="text-muted">Supported formats: PDF, DOC, DOCX, JPG, JPEG, PNG (Max: 10MB)</small>
          </div>
          {{ form.file }}
          {% if form.file.help_text %}
            <small class="form-text text-muted">{{ form.file.help_text }}</small>
          {% endif %}
          {% if form.file.errors %}
            <div class="text-danger small mt-1">
              {% for error in form.file.errors %}{{ error }}{% endfor %}
            </div>
          {% endif %}
        </div>

        <!-- Description -->
        <div class="col-md-8 mb-3">
          <label for="{{ form.description.id_for_label }}" class="form-label fw-semibold">
            {{ form.description.label }}
          </label>
          {{ form.description }}
          {% if form.description.help_text %}
            <small class="form-text text-muted">{{ form.description.help_text }}</small>
          {% endif %}
          {% if form.description.errors %}
            <div class="text-danger small mt-1">
              {% for error in form.description.errors %}{{ error }}{% endfor %}
            </div>
          {% endif %}
        </div>

        <!-- Expiry Date -->
        <div class="col-md-4 mb-3">
          <label for="{{ form.expiry_date.id_for_label }}" class="form-label fw-semibold">
            {{ form.expiry_date.label }}
          </label>
          {{ form.expiry_date }}
          {% if form.expiry_date.help_text %}
            <small class="form-text text-muted">{{ form.expiry_date.help_text }}</small>
          {% endif %}
          {% if form.expiry_date.errors %}
            <div class="text-danger small mt-1">
              {% for error in form.expiry_date.errors %}{{ error }}{% endfor %}
            </div>
          {% endif %}
        </div>
      </div>

      <!-- Form buttons -->
      <div class="d-flex justify-content-between mt-4">
        <a href="{% url 'all_vendor_documents' %}" class="btn btn-secondary">
          <i class="bi bi-arrow-left"></i> Cancel
        </a>
        <button type="submit" class="btn btn-primary">
          <i class="bi bi-save"></i> Save Document
        </button>
      </div>
    </form>
  </div>
</div>

<script>
// File upload preview
document.getElementById('{{ form.file.id_for_label }}').addEventListener('change', function(e) {
    const file = e.target.files[0];
    const uploadInfo = document.querySelector('.file-upload-info');
    
    if (file) {
        uploadInfo.innerHTML = `
            <i class="bi bi-file-earmark-check fs-2 text-success"></i>
            <p class="mb-1 text-success">File selected: ${file.name}</p>
            <small class="text-muted">Size: ${(file.size / 1024 / 1024).toFixed(2)} MB</small>
        `;
    }
});
</script>
{% endblock %}
