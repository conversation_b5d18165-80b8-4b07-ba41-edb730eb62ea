{% extends 'base.html' %}
{% load static %}

{% block title %}Confirm Document Deletion{% endblock %}

{% block content %}
<div class="container mt-4">
  <div class="card shadow">
    <div class="card-header bg-danger text-white">
      <h3><i class="bi bi-exclamation-triangle-fill me-2"></i>Confirm Document Deletion</h3>
    </div>
    <div class="card-body">
      <div class="alert alert-warning">
        <p class="mb-0"><strong>Warning:</strong> This action cannot be undone. The document file will be permanently deleted from the system.</p>
      </div>
      
      <div class="card mb-4">
        <div class="card-header bg-light">
          <h5><i class="bi bi-file-earmark-text me-2"></i>Document Information</h5>
        </div>
        <div class="card-body">
          <table class="table table-borderless">
            <tr>
              <th style="width: 30%">Vendor:</th>
              <td>{{ document.vendor.name }}</td>
            </tr>
            <tr>
              <th>Document Type:</th>
              <td>{{ document.get_document_type_display }}</td>
            </tr>
            <tr>
              <th>File Name:</th>
              <td>
                <i class="bi bi-file-earmark me-1"></i>
                {{ document.file.name|default:"No file" }}
              </td>
            </tr>
            {% if document.description %}
            <tr>
              <th>Description:</th>
              <td>{{ document.description }}</td>
            </tr>
            {% endif %}
            {% if document.expiry_date %}
            <tr>
              <th>Expiry Date:</th>
              <td>{{ document.expiry_date|date:"F d, Y" }}</td>
            </tr>
            {% endif %}
            <tr>
              <th>Upload Date:</th>
              <td>{{ document.upload_date|date:"F d, Y g:i A" }}</td>
            </tr>
          </table>
        </div>
      </div>
      
      <p class="lead text-center mb-4">Are you sure you want to delete this document?</p>
      
      <form method="post" class="d-flex justify-content-center gap-3">
        {% csrf_token %}
        <button type="submit" class="btn btn-danger">
          <i class="bi bi-trash me-1"></i> Confirm Delete
        </button>
        <a href="{% url 'vendor_document_list' vendor_id=document.vendor.id %}" class="btn btn-secondary">
          <i class="bi bi-x-circle me-1"></i> Cancel
        </a>
      </form>
    </div>
  </div>
</div>
{% endblock %}
