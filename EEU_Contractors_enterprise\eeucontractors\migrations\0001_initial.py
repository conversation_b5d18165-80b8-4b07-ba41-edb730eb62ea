# Generated by Django 5.2.3 on 2025-06-29 08:42

import django.contrib.auth.validators
import django.core.validators
import django.db.models.deletion
import django.utils.timezone
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('auth', '0012_alter_user_first_name_max_length'),
    ]

    operations = [
        migrations.CreateModel(
            name='EEUHeadOffice',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(default='EEU Head Office', max_length=100)),
            ],
            options={
                'verbose_name': 'EEU Head Office',
                'verbose_name_plural': 'EEU Head Office',
            },
        ),
        migrations.CreateModel(
            name='MainActivity',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('main_activity', models.CharField(choices=[('substation', '66 and 45 kV substation electrical and civil works'), ('lines', 'Underground and Overhead 66, 45, 33, 19 and 15 kV lines construction'), ('transformers', 'Installation of 33,19 and 15KV transformers and switchgear'), ('low_voltage', '0.4/0.23 kV line construction and maintenance'), ('meters', 'Installation and connection of single and three-phase meters')], max_length=50)),
                ('levels', models.CharField(choices=[('level_1_2_3', 'Level 1, 2 or 3'), ('level_4_5_6', 'Level 4, 5 or 6'), ('level_7_8', 'Level 7 or 8')], max_length=20)),
            ],
        ),
        migrations.CreateModel(
            name='Region',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(choices=[('EAST_AA', 'EAST AA'), ('SOUTH_AA', 'SOUTH AA'), ('WEST_AA', 'WEST AA '), ('NORTH_AA', 'NORTH AA'), ('AFAR', 'AFAR'), ('BAHIRDAR', 'BAHIRDAR'), ('DESSIE', 'DESSIE'), ('GONDER', 'GONDER'), ('DEBRE_BIRHAN', 'DEBRE BIRHAN'), ('DEBRE_MARKOS', 'DEBRE MARKOS '), ('WOLEDIYA', 'WOLEDIYA'), ('BENISH', 'Benishangule Gumuz'), ('DIRE_DAWA', 'DIRE DAWA '), ('GAMBELA', 'GAMBELA'), ('HARERE', 'HARERE'), ('ADAMA', 'ADAMA'), ('SHEGER', 'Sheger '), ('SHASHEMENE', 'SHASHEMENE'), ('CHIRO', 'CHIRO'), ('JIMMA', 'JIMMA'), ('NEKEMITE', 'NEKEMITE'), ('AMBO', 'AMBO'), ('BALE_ROBE', 'BALE ROBE'), ('METU', 'METU'), ('SOMALI', 'SOMALI'), ('ARBAMINICH', 'ARBAMINICH '), ('CENTRAL_ETHIOPIA', 'Central Ethiopia'), ('WELAYITA', 'WELAYITA'), ('MEKELE', 'MEKELE'), ('SHIRE', 'SHIRE '), ('SIDAMA', 'SIDAMA'), ('SOUTH_WEST', 'SOUTH WEST'), ('SOUTH_ETHIOPIA', 'SOUTH ETHIOPIA')], max_length=100, unique=True)),
                ('region_code', models.CharField(choices=[('AA', 'AA'), ('AB', 'AB'), ('AC', 'AC'), ('AD', 'AD'), ('BA', 'BA'), ('BB', 'BB'), ('BC', 'BC'), ('BD', 'BD'), ('BE', 'BE'), ('BF', 'BF'), ('BG', 'BG'), ('BH', 'BH'), ('BI', 'BI'), ('CA', 'CA'), ('CB', 'CB'), ('CC', 'CC'), ('CD', 'CD'), ('CE', 'CE'), ('CF', 'CF'), ('DB', 'DB'), ('DC', 'DC'), ('DE', 'DE'), ('EA', 'EA'), ('EB', 'EB'), ('FA', 'FA'), ('GA', 'GA'), ('HA', 'HA'), ('IA', 'IA'), ('JA', 'JA'), ('KA', 'KA'), ('LA', 'LA'), ('MA', 'MA')], max_length=10)),
            ],
            options={
                'ordering': ['region_code', 'name'],
            },
        ),
        migrations.CreateModel(
            name='Vendor',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(editable=False, max_length=255, null=True)),
                ('level', models.CharField(editable=False, max_length=100)),
                ('type', models.CharField(editable=False, max_length=100)),
                ('identification_number', models.CharField(editable=False, max_length=100)),
                ('phone_number', models.CharField(editable=False, max_length=20)),
                ('experience', models.PositiveIntegerField(editable=False)),
                ('email', models.EmailField(editable=False, max_length=254)),
                ('status', models.CharField(choices=[('active', 'Active'), ('inactive', 'Inactive')], default='inactive', editable=False, max_length=10)),
            ],
        ),
        migrations.CreateModel(
            name='CustomUser',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('password', models.CharField(max_length=128, verbose_name='password')),
                ('last_login', models.DateTimeField(blank=True, null=True, verbose_name='last login')),
                ('is_superuser', models.BooleanField(default=False, help_text='Designates that this user has all permissions without explicitly assigning them.', verbose_name='superuser status')),
                ('username', models.CharField(error_messages={'unique': 'A user with that username already exists.'}, help_text='Required. 150 characters or fewer. Letters, digits and @/./+/-/_ only.', max_length=150, unique=True, validators=[django.contrib.auth.validators.UnicodeUsernameValidator()], verbose_name='username')),
                ('first_name', models.CharField(blank=True, max_length=150, verbose_name='first name')),
                ('last_name', models.CharField(blank=True, max_length=150, verbose_name='last name')),
                ('email', models.EmailField(blank=True, max_length=254, verbose_name='email address')),
                ('is_staff', models.BooleanField(default=False, help_text='Designates whether the user can log into this admin site.', verbose_name='staff status')),
                ('is_active', models.BooleanField(default=True, help_text='Designates whether this user should be treated as active. Unselect this instead of deleting accounts.', verbose_name='active')),
                ('date_joined', models.DateTimeField(default=django.utils.timezone.now, verbose_name='date joined')),
                ('role', models.CharField(choices=[('ho_admin', 'Head Office Administrator'), ('ho_manager', 'Head Office Manager'), ('regional_admin', 'Regional Administrator'), ('regional_manager', 'Regional Manager'), ('project_coordinator', 'Project Coordinator'), ('data_entry', 'Data Entry Operator')], default='data_entry', max_length=20)),
                ('groups', models.ManyToManyField(blank=True, help_text='The groups this user belongs to. A user will get all permissions granted to each of their groups.', related_name='user_set', related_query_name='user', to='auth.group', verbose_name='groups')),
                ('user_permissions', models.ManyToManyField(blank=True, help_text='Specific permissions for this user.', related_name='user_set', related_query_name='user', to='auth.permission', verbose_name='user permissions')),
                ('region', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='eeucontractors.region')),
            ],
            options={
                'verbose_name': 'user',
                'verbose_name_plural': 'users',
                'abstract': False,
            },
        ),
        migrations.CreateModel(
            name='Project',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100)),
                ('level', models.PositiveIntegerField(help_text='Select vendor level between 1 and 8', validators=[django.core.validators.MinValueValidator(1), django.core.validators.MaxValueValidator(8)])),
                ('type', models.CharField(choices=[('contractors', 'Contractors'), ('enterprise', 'Enterprise'), ('new_enterprise', 'New Enterprise')], max_length=20)),
                ('start_date', models.DateField()),
                ('estimated_end_date', models.DateField()),
                ('end_date', models.DateField(blank=True, null=True)),
                ('time_consumption', models.DurationField(blank=True, null=True)),
                ('status', models.CharField(choices=[('active', 'Active'), ('suspended', 'Suspended'), ('completed', 'Completed'), ('cancelled', 'Cancelled')], max_length=20)),
                ('approved', models.BooleanField(default=False)),
                ('approved_by', models.CharField(blank=True, max_length=100, null=True)),
                ('approved_date', models.DateField(blank=True, null=True)),
                ('main_activity', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='eeucontractors.mainactivity')),
                ('region', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='eeucontractors.region')),
                ('vendor', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='projects', to='eeucontractors.vendor')),
            ],
        ),
        migrations.CreateModel(
            name='ProjectProgress',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('progress_percentage', models.DecimalField(decimal_places=2, default=0, max_digits=5)),
                ('last_updated', models.DateTimeField(auto_now=True)),
                ('start_date', models.DateField(default=django.utils.timezone.now)),
                ('completion_date', models.DateField(blank=True, null=True)),
                ('status', models.CharField(choices=[('on-going', 'On-going'), ('delayed', 'Delayed'), ('suspended', 'Suspended'), ('completed', 'Completed'), ('completed with delay', 'Completed with delay')], max_length=30)),
                ('remarks', models.TextField(blank=True, null=True)),
                ('main_activity', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='eeucontractors.mainactivity')),
                ('project', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='progress', to='eeucontractors.project')),
                ('updated_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to=settings.AUTH_USER_MODEL)),
                ('vendor', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='eeucontractors.vendor')),
            ],
            options={
                'ordering': ['-start_date'],
            },
        ),
        migrations.CreateModel(
            name='RegionalPerformance',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('total_projects', models.PositiveIntegerField(default=0)),
                ('completed_projects', models.PositiveIntegerField(default=0)),
                ('active_projects', models.PositiveIntegerField(default=0)),
                ('delayed_projects', models.PositiveIntegerField(default=0)),
                ('total_vendors', models.PositiveIntegerField(default=0)),
                ('active_vendors', models.PositiveIntegerField(default=0)),
                ('blacklisted_vendors', models.PositiveIntegerField(default=0)),
                ('average_project_completion_time', models.FloatField(default=0.0, help_text='Average days to complete projects')),
                ('average_progress_percentage', models.FloatField(default=0.0, help_text='Average progress across all active projects')),
                ('total_project_value', models.DecimalField(decimal_places=2, default=0, help_text='Total value of all projects', max_digits=15)),
                ('completed_project_value', models.DecimalField(decimal_places=2, default=0, help_text='Value of completed projects', max_digits=15)),
                ('last_updated', models.DateTimeField(auto_now=True)),
                ('region', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='performance', to='eeucontractors.region')),
            ],
            options={
                'verbose_name': 'Regional Performance',
                'verbose_name_plural': 'Regional Performance',
            },
        ),
        migrations.CreateModel(
            name='SubActivity',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=200)),
                ('order', models.PositiveIntegerField()),
                ('weight', models.DecimalField(decimal_places=2, max_digits=5)),
                ('base_duration', models.DecimalField(decimal_places=2, help_text='Base duration per unit in days', max_digits=6)),
                ('main_activity', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='subactivities', to='eeucontractors.mainactivity')),
            ],
        ),
        migrations.CreateModel(
            name='SubActivityWorkload',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('quantity', models.PositiveIntegerField(default=1, help_text='Total quantity of work units', validators=[django.core.validators.MinValueValidator(1)])),
                ('completed_quantity', models.PositiveIntegerField(default=0, help_text='Completed quantity of work units', validators=[django.core.validators.MinValueValidator(0)])),
                ('unit_type', models.CharField(default='units', help_text='Type of units (poles, meters, transformers, etc.)', max_length=50)),
                ('expected_duration', models.PositiveIntegerField(default=1, help_text='Expected duration in days', validators=[django.core.validators.MinValueValidator(1)])),
                ('project_progress', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='workloads', to='eeucontractors.projectprogress')),
                ('sub_activity', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='eeucontractors.subactivity')),
            ],
            options={
                'ordering': ['sub_activity__order'],
                'unique_together': {('project_progress', 'sub_activity')},
            },
        ),
        migrations.CreateModel(
            name='ProjectVendorsHistory',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('status', models.CharField(choices=[('completed', 'Completed'), ('delayed', 'Delayed'), ('suspended', 'Suspended'), ('blacklisted', 'Blacklisted')], default='completed', max_length=20)),
                ('evaluation_notes', models.TextField(blank=True, null=True)),
                ('start_date', models.DateField()),
                ('end_date', models.DateField()),
                ('cause_of_delay_or_suspension', models.CharField(blank=True, choices=[('vendor', 'Vendor'), ('organization', 'Organization'), ('both', 'Both'), ('unknown', 'Unknown')], help_text='Specify who caused the delay or suspension if applicable', max_length=20, null=True)),
                ('project', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, to='eeucontractors.project')),
                ('vendor', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='eeucontractors.vendor')),
            ],
        ),
        migrations.CreateModel(
            name='VendorDocument',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('document_type', models.CharField(choices=[('license', 'License'), ('certificate', 'Certificate'), ('id_proof', 'ID Proof'), ('other', 'Other')], max_length=50)),
                ('file', models.FileField(upload_to='vendor_documents/')),
                ('description', models.TextField(blank=True, max_length=100)),
                ('expiry_date', models.DateField()),
                ('uploaded_at', models.DateTimeField(auto_now_add=True)),
                ('vendor', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='documents', to='eeucontractors.vendor')),
            ],
        ),
        migrations.CreateModel(
            name='Vendorsregistration',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('contractor_type', models.CharField(choices=[('contractor', 'Contractor'), ('enterprise', 'Enterprise'), ('new_enterprise', 'New Enterprise')], max_length=20, verbose_name='የኮንትራክተሩ አይነት')),
                ('contractor_name', models.CharField(max_length=255, verbose_name='የተመዘገበው ስም')),
                ('phone_number', models.CharField(max_length=20, verbose_name='ስልክ ቁጥር')),
                ('experience', models.PositiveIntegerField(help_text='Years of experience', verbose_name='የስራ ልምድ (በዓመት)')),
                ('email', models.EmailField(max_length=254, verbose_name='ኢሜይል አድራሻ')),
                ('identification_number', models.CharField(blank=True, max_length=100, unique=True, verbose_name='የመወዳደሪያ መለያ ቁጥር')),
                ('has_professional_license', models.BooleanField(default=False, verbose_name='የሙያ ብቃት አቅርቧል?')),
                ('electric_grade', models.CharField(blank=True, choices=[('1', 'Level 1'), ('2', 'Level 2'), ('3', 'Level 3'), ('4', 'Level 4'), ('5', 'Level 5'), ('6', 'Level 6'), ('7', 'Level 7'), ('8', 'Level 8')], max_length=2, verbose_name='የኤሌክትሪክ ስራ ተቋራጭ ደረጃ')),
                ('has_human_resource', models.BooleanField(default=False, verbose_name='የሰው ኃይል አሟልቷል?')),
                ('electrical_engineers', models.PositiveIntegerField(validators=[django.core.validators.MinValueValidator(1)], verbose_name='የኤሌክትሪካል ምህንድስና')),
                ('electromechanical_engineers', models.PositiveIntegerField(validators=[django.core.validators.MinValueValidator(1)], verbose_name='የኤሌክትሮ መካኒካል ምህንድስና')),
                ('civil_engineers', models.PositiveIntegerField(validators=[django.core.validators.MinValueValidator(1)], verbose_name='የሲቪል ምህንድስና')),
                ('electricians_foremen', models.PositiveIntegerField(validators=[django.core.validators.MinValueValidator(1)], verbose_name='ኤሌክትሪሺያን/ፎርማን')),
                ('surveyors', models.PositiveIntegerField(validators=[django.core.validators.MinValueValidator(1)], verbose_name='ሰርቬየር')),
                ('line_workers', models.PositiveIntegerField(validators=[django.core.validators.MinValueValidator(1)], verbose_name='የመስመር ሰራተኞች')),
                ('has_work_experience_doc', models.BooleanField(default=False, verbose_name='የሥራ ልምድ ማስረጃ አቅርቧል?')),
                ('has_trade_license', models.BooleanField(default=False, verbose_name='የንግድ ፈቃድ አለው?')),
                ('has_energy_certification', models.BooleanField(default=False, verbose_name='የኤሌክትሪክ ደረጃ የምስክር ወረቀት አቅርቧል?')),
                ('has_equipment_resources', models.BooleanField(default=False, verbose_name='የሰው ኃይልና መሳሪያ አሟልቷል?')),
                ('has_vat_and_tax', models.BooleanField(default=False, verbose_name='VAT እና የግብር የምስክር ወረቀት አቅርቧል?')),
                ('has_good_performance', models.BooleanField(default=False, verbose_name='የመልካም ስራ አፈጻጸም አቅርቧል?')),
                ('has_financial_capacity', models.BooleanField(default=False, verbose_name='የፋይናንስ አቅም መረጃ አቅርቧል?')),
                ('registrar_name', models.CharField(max_length=255, verbose_name='የመዝጋቢ ሰራተኛው ስም')),
                ('registrar_id', models.CharField(max_length=50, verbose_name='መለያ ቁጥር')),
                ('registration_date', models.DateTimeField(default=django.utils.timezone.now, verbose_name='የተመዘገበበት ቀን')),
                ('region', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='vendors', to='eeucontractors.region', verbose_name='ሪጅን')),
            ],
        ),
        migrations.AddField(
            model_name='vendor',
            name='vendor_registration',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='eeucontractors.vendorsregistration'),
        ),
        migrations.CreateModel(
            name='SubActivityProgress',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('progress', models.PositiveSmallIntegerField(validators=[django.core.validators.MinValueValidator(0), django.core.validators.MaxValueValidator(100)])),
                ('start_date', models.DateField()),
                ('end_date', models.DateField()),
                ('workload', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='progress_records', to='eeucontractors.subactivityworkload')),
            ],
            options={
                'ordering': ['-end_date'],
                'indexes': [models.Index(fields=['workload', '-end_date'], name='eeucontract_workloa_3a986b_idx')],
            },
        ),
        migrations.CreateModel(
            name='RegionalProjectTemplate',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('is_available', models.BooleanField(default=True, help_text='Is this project slot available for assignment?')),
                ('approved_by_director', models.BooleanField(default=False)),
                ('director_approval_date', models.DateTimeField(blank=True, null=True)),
                ('director_notes', models.TextField(blank=True, help_text="Director's notes about this project assignment")),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('main_activity', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='eeucontractors.mainactivity')),
                ('project', models.OneToOneField(blank=True, help_text='Actual project if assigned', null=True, on_delete=django.db.models.deletion.SET_NULL, to='eeucontractors.project')),
                ('region', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='project_templates', to='eeucontractors.region')),
                ('assigned_vendor', models.ForeignKey(blank=True, help_text='Vendor assigned to this project', null=True, on_delete=django.db.models.deletion.SET_NULL, to='eeucontractors.vendor')),
            ],
            options={
                'verbose_name': 'Regional Project Template',
                'verbose_name_plural': 'Regional Project Templates',
                'ordering': ['region__name', 'main_activity__main_activity'],
                'unique_together': {('region', 'main_activity')},
            },
        ),
    ]
