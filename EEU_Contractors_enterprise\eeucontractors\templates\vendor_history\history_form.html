{% extends 'base.html' %}
{% load static %}

{% block title %}{{ title }}{% endblock %}

{% block content %}
<div class="container mt-4">
  <div class="card shadow">
    <div class="card-header bg-primary text-white">
      <h3><i class="bi bi-clock-history me-2"></i>{{ title }}</h3>
    </div>
    <div class="card-body">
      {% include 'components/messages.html' %}

      <!-- Display non-field errors -->
      {% if form.non_field_errors %}
        <div class="alert alert-danger">
          <strong>Error:</strong>
          {% for error in form.non_field_errors %}
            <div>{{ error }}</div>
          {% endfor %}
        </div>
      {% endif %}
      
      <form method="post">
        {% csrf_token %}
        
        <div class="row">
          {% for field in form %}
            <div class="col-md-6 mb-3">
              {% if field.is_hidden %}
                {{ field }}
              {% else %}
                <label for="{{ field.id_for_label }}" class="form-label fw-semibold">
                  {{ field.label }}
                  {% if field.field.required %}
                    <span class="text-danger">*</span>
                  {% endif %}
                </label>
                {{ field }}
                {% if field.help_text %}
                  <small class="form-text text-muted">{{ field.help_text }}</small>
                {% endif %}
                {% if field.errors %}
                  <div class="invalid-feedback d-block">
                    {% for error in field.errors %}
                      {{ error }}
                    {% endfor %}
                  </div>
                {% endif %}
              {% endif %}
            </div>
          {% endfor %}
        </div>
        
        <div class="mt-4 d-flex justify-content-between">
          <a href="{% url 'vendor_history_list' %}" class="btn btn-secondary">
            <i class="bi bi-arrow-left"></i> Back to History
          </a>
          <button type="submit" class="btn btn-primary">
            <i class="bi bi-save"></i> Save History Record
          </button>
        </div>
      </form>
    </div>
  </div>
</div>
{% endblock %}
