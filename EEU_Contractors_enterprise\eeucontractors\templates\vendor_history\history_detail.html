{% extends 'base.html' %}
{% load static %}

{% block title %}Vendor History Detail{% endblock %}

{% block content %}
<div class="container mt-4">
  <div class="d-flex justify-content-between align-items-center mb-4">
    <h2><i class="bi bi-clock-history me-2"></i>Vendor History Detail</h2>
    <div>
      <a href="{% url 'edit_vendor_history' history_id=history.id %}" class="btn btn-warning">
        <i class="bi bi-pencil"></i> Edit
      </a>
      <a href="{% url 'vendor_history_list' %}" class="btn btn-secondary">
        <i class="bi bi-arrow-left"></i> Back to List
      </a>
    </div>
  </div>

  <div class="row">
    <!-- Basic Information -->
    <div class="col-md-8">
      <div class="card mb-4">
        <div class="card-header bg-info text-white">
          <h5><i class="bi bi-info-circle me-2"></i>History Information</h5>
        </div>
        <div class="card-body">
          <table class="table table-borderless">
            <tr>
              <th style="width: 30%">Vendor:</th>
              <td>{{ history.vendor.name }}</td>
            </tr>
            <tr>
              <th>Project:</th>
              <td>{{ history.project.name }}</td>
            </tr>
            <tr>
              <th>Status:</th>
              <td>
                {% if history.status == 'completed' %}
                  <span class="badge bg-success">{{ history.get_status_display }}</span>
                {% elif history.status == 'delayed' %}
                  <span class="badge bg-warning">{{ history.get_status_display }}</span>
                {% elif history.status == 'suspended' %}
                  <span class="badge bg-danger">{{ history.get_status_display }}</span>
                {% else %}
                  <span class="badge bg-secondary">{{ history.get_status_display }}</span>
                {% endif %}
              </td>
            </tr>
            <tr>
              <th>Start Date:</th>
              <td>{{ history.start_date|date:"F d, Y" }}</td>
            </tr>
            <tr>
              <th>End Date:</th>
              <td>
                {% if history.end_date %}
                  {{ history.end_date|date:"F d, Y" }}
                {% else %}
                  <span class="text-muted">Not completed</span>
                {% endif %}
              </td>
            </tr>
            {% if history.cause_of_delay_or_suspension %}
            <tr>
              <th>Delay/Suspension Cause:</th>
              <td>
                <span class="badge bg-warning">{{ history.get_cause_of_delay_or_suspension_display }}</span>
              </td>
            </tr>
            {% endif %}
          </table>
        </div>
      </div>

      <!-- Evaluation Notes -->
      {% if history.evaluation_notes %}
      <div class="card">
        <div class="card-header bg-secondary text-white">
          <h5><i class="bi bi-chat-text me-2"></i>Evaluation Notes</h5>
        </div>
        <div class="card-body">
          <p class="mb-0">{{ history.evaluation_notes|linebreaks }}</p>
        </div>
      </div>
      {% endif %}
    </div>

    <!-- Summary Card -->
    <div class="col-md-4">
      <div class="card">
        <div class="card-header bg-primary text-white">
          <h5><i class="bi bi-graph-up me-2"></i>Summary</h5>
        </div>
        <div class="card-body">
          <div class="mb-3">
            <strong>Duration:</strong><br>
            {% if history.end_date %}
              {{ history.start_date|timesince:history.end_date }}
            {% else %}
              <span class="text-muted">Ongoing</span>
            {% endif %}
          </div>
          
          <div class="mb-3">
            <strong>Vendor Type:</strong><br>
            {{ history.vendor.get_contractor_type_display|default:"N/A" }}
          </div>
          
          <div class="mb-3">
            <strong>Vendor Level:</strong><br>
            Level {{ history.vendor.electric_grade|default:"N/A" }}
          </div>
          
          <div class="mb-3">
            <strong>Region:</strong><br>
            {{ history.project.region.name|default:"N/A" }}
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
{% endblock %}
