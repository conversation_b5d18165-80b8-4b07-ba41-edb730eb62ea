{% extends 'base.html' %}
{% load static %}

{% block content %}
<div class="container-fluid mt-4">
  <!-- <PERSON> Header -->
  <div class="row mb-4">
    <div class="col-12">
      <h2 class="text-success fw-bold">EEU Contractors Dashboard</h2>
      <p class="text-muted">Project Progress & Performance Overview</p>
    </div>
  </div>

  <!-- Summary Cards -->
  <div class="row mb-4">
    <div class="col-lg-3 col-md-6 mb-3">
      <div class="card border-success">
        <div class="card-body text-center">
          <div class="text-success mb-2">
            <i class="bi bi-building" style="font-size: 2rem;"></i>
          </div>
          <h4 class="text-success">{{ total_projects }}</h4>
          <p class="text-muted mb-0">Total Projects</p>
        </div>
      </div>
    </div>
    
    <div class="col-lg-3 col-md-6 mb-3">
      <div class="card border-warning">
        <div class="card-body text-center">
          <div class="text-warning mb-2">
            <i class="bi bi-people" style="font-size: 2rem;"></i>
          </div>
          <h4 class="text-warning">{{ total_vendors }}</h4>
          <p class="text-muted mb-0">Active Vendors</p>
        </div>
      </div>
    </div>
    
    <div class="col-lg-3 col-md-6 mb-3">
      <div class="card border-success">
        <div class="card-body text-center">
          <div class="text-success mb-2">
            <i class="bi bi-check-circle" style="font-size: 2rem;"></i>
          </div>
          <h4 class="text-success">{{ completed_projects }}</h4>
          <p class="text-muted mb-0">Completed Projects</p>
        </div>
      </div>
    </div>
    
    <div class="col-lg-3 col-md-6 mb-3">
      <div class="card border-warning">
        <div class="card-body text-center">
          <div class="text-warning mb-2">
            <i class="bi bi-clock" style="font-size: 2rem;"></i>
          </div>
          <h4 class="text-warning">{{ ongoing_projects }}</h4>
          <p class="text-muted mb-0">Ongoing Projects</p>
        </div>
      </div>
    </div>
  </div>

  <!-- Progress Overview -->
  <div class="row mb-4">
    <div class="col-lg-8">
      <div class="card">
        <div class="card-header" style="background-color: #f8f9fa; border-bottom: 1px solid #e9ecef;">
          <h5 class="mb-0 text-dark"><i class="bi bi-graph-up me-2"></i>Recent Project Progress</h5>
        </div>
        <div class="card-body">
          <div class="table-responsive">
            <table class="table table-hover">
              <thead class="table-light">
                <tr>
                  <th>Project</th>
                  <th>Vendor</th>
                  <th>Progress</th>
                  <th>Status</th>
                  <th>Action</th>
                </tr>
              </thead>
              <tbody>
                {% for progress in recent_progress %}
                <tr>
                  <td>{{ progress.project.name }}</td>
                  <td>{{ progress.vendor.name }}</td>
                  <td>
                    <div class="progress" style="height: 20px;">
                      <div class="progress-bar bg-success"
                           style="width: {{ progress.progress_percentage }}%;">
                        {{ progress.progress_percentage }}%
                      </div>
                    </div>
                  </td>
                  <td>
                    <span class="badge {% if progress.status == 'completed' %}bg-success{% elif progress.status == 'on-going' %}bg-warning{% else %}bg-secondary{% endif %}">
                      {{ progress.get_status_display }}
                    </span>
                  </td>
                  <td>
                    <a href="{% url 'select_project_progress' progress_id=progress.id %}"
                       class="text-decoration-underline"
                       style="color: orange; font-weight: bold;">
                      View Details
                    </a>
                  </td>
                </tr>
                {% empty %}
                <tr>
                  <td colspan="5" class="text-center text-muted">No project progress data available</td>
                </tr>
                {% endfor %}
              </tbody>
            </table>
          </div>
        </div>
      </div>
    </div>
    
    <div class="col-lg-4">
      <div class="card">
        <div class="card-header" style="background-color: #fff3cd; border-bottom: 1px solid #ffeaa7;">
          <h5 class="mb-0 text-dark"><i class="bi bi-exclamation-triangle me-2"></i>Project Status</h5>
        </div>
        <div class="card-body">
          <div class="mb-3">
            <div class="d-flex justify-content-between">
              <span>On-going</span>
              <span class="text-warning fw-bold">{{ status_counts.on_going|default:0 }}</span>
            </div>
            <div class="progress mb-2" style="height: 8px;">
              <div class="progress-bar bg-warning" style="width: {{ status_percentages.on_going|default:0 }}%;"></div>
            </div>
          </div>
          
          <div class="mb-3">
            <div class="d-flex justify-content-between">
              <span>Completed</span>
              <span class="text-success fw-bold">{{ status_counts.completed|default:0 }}</span>
            </div>
            <div class="progress mb-2" style="height: 8px;">
              <div class="progress-bar bg-success" style="width: {{ status_percentages.completed|default:0 }}%;"></div>
            </div>
          </div>
          
          <div class="mb-3">
            <div class="d-flex justify-content-between">
              <span>Delayed</span>
              <span class="text-danger fw-bold">{{ status_counts.delayed|default:0 }}</span>
            </div>
            <div class="progress mb-2" style="height: 8px;">
              <div class="progress-bar bg-danger" style="width: {{ status_percentages.delayed|default:0 }}%;"></div>
            </div>
          </div>
          
          <div class="mb-3">
            <div class="d-flex justify-content-between">
              <span>Suspended</span>
              <span class="text-secondary fw-bold">{{ status_counts.suspended|default:0 }}</span>
            </div>
            <div class="progress mb-2" style="height: 8px;">
              <div class="progress-bar bg-secondary" style="width: {{ status_percentages.suspended|default:0 }}%;"></div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Search & Filter Section -->
  <div class="row mb-4">
    <div class="col-12">
      <div class="card">
        <div class="card-header" style="background-color: #fff3cd; border-bottom: 1px solid #ffeaa7;">
          <h5 class="mb-0 text-dark"><i class="bi bi-search me-2"></i>Quick Search & Filters</h5>
        </div>
        <div class="card-body">
          <form method="get" action="{% url 'dashboard' %}" class="row g-3">
            <div class="col-md-4">
              <label for="search_project" class="form-label">Search Projects</label>
              <div class="input-group">
                <input type="text" class="form-control" id="search_project" name="search_project"
                       placeholder="Enter project name..." value="{{ request.GET.search_project }}">
                <button class="btn btn-outline-success" type="submit">
                  <i class="bi bi-search"></i>
                </button>
              </div>
            </div>

            <div class="col-md-3">
              <label for="filter_vendor" class="form-label">Filter by Vendor</label>
              <select class="form-select" id="filter_vendor" name="filter_vendor">
                <option value="">All Vendors</option>
                {% for vendor in all_vendors %}
                <option value="{{ vendor.id }}" {% if request.GET.filter_vendor == vendor.id|stringformat:"s" %}selected{% endif %}>
                  {{ vendor.name }}
                </option>
                {% endfor %}
              </select>
            </div>

            <div class="col-md-3">
              <label for="filter_status" class="form-label">Filter by Status</label>
              <select class="form-select" id="filter_status" name="filter_status">
                <option value="">All Status</option>
                <option value="on-going" {% if request.GET.filter_status == "on-going" %}selected{% endif %}>On-going</option>
                <option value="completed" {% if request.GET.filter_status == "completed" %}selected{% endif %}>Completed</option>
                <option value="delayed" {% if request.GET.filter_status == "delayed" %}selected{% endif %}>Delayed</option>
                <option value="suspended" {% if request.GET.filter_status == "suspended" %}selected{% endif %}>Suspended</option>
              </select>
            </div>

            <div class="col-md-2">
              <label class="form-label">&nbsp;</label>
              <div class="d-grid">
                <a href="{% url 'dashboard' %}" class="btn btn-outline-secondary">
                  <i class="bi bi-arrow-clockwise"></i> Reset
                </a>
              </div>
            </div>
          </form>

          <!-- Quick Stats Row -->
          <div class="row mt-4 pt-3 border-top">
            <div class="col-md-3 text-center">
              <h6 class="text-success">{{ filtered_stats.total_found }}</h6>
              <small class="text-muted">Projects Found</small>
            </div>
            <div class="col-md-3 text-center">
              <h6 class="text-warning">{{ filtered_stats.avg_progress|floatformat:1 }}%</h6>
              <small class="text-muted">Avg Progress</small>
            </div>
            <div class="col-md-3 text-center">
              <h6 class="text-info">{{ filtered_stats.active_vendors }}</h6>
              <small class="text-muted">Active Vendors</small>
            </div>
            <div class="col-md-3 text-center">
              <h6 class="text-success">{{ filtered_stats.completed_count }}</h6>
              <small class="text-muted">Completed</small>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>





  <!-- Charts Section -->
  <div class="row">
    <div class="col-lg-6 mx-auto mb-4">
      <div class="card">
        <div class="card-header" style="background-color: #d4edda; border-bottom: 1px solid #c3e6cb;">
          <h6 class="mb-0 text-dark"><i class="bi bi-pie-chart me-2"></i>Project Status Distribution</h6>
        </div>
        <div class="card-body">
          <canvas id="statusPieChart" width="400" height="300"></canvas>
        </div>
      </div>
    </div>
  </div>
</div>

<!-- Chart.js CDN -->
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>

<script>
// Pie Chart - Project Status Distribution
const statusCtx = document.getElementById('statusPieChart').getContext('2d');
const statusPieChart = new Chart(statusCtx, {
    type: 'doughnut',
    data: {
        labels: ['Completed', 'On-going', 'Delayed', 'Suspended'],
        datasets: [{
            data: [
                {{ status_counts.completed|default:0 }},
                {{ status_counts.on_going|default:0 }},
                {{ status_counts.delayed|default:0 }},
                {{ status_counts.suspended|default:0 }}
            ],
            backgroundColor: [
                '#28a745', // Green for completed
                '#ffc107', // Orange for on-going
                '#dc3545', // Red for delayed
                '#6c757d'  // Gray for suspended
            ],
            borderWidth: 3,
            borderColor: '#fff',
            hoverBorderWidth: 4,
            hoverBorderColor: '#fff'
        }]
    },
    options: {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
            legend: {
                position: 'bottom',
                labels: {
                    padding: 20,
                    usePointStyle: true,
                    font: {
                        size: 12,
                        weight: 'bold'
                    }
                }
            },
            tooltip: {
                callbacks: {
                    label: function(context) {
                        const label = context.label || '';
                        const value = context.parsed;
                        const total = context.dataset.data.reduce((a, b) => a + b, 0);
                        const percentage = total > 0 ? ((value / total) * 100).toFixed(1) : 0;
                        return `${label}: ${value} projects (${percentage}%)`;
                    }
                }
            }
        },
        cutout: '50%', // Makes it a doughnut chart
        animation: {
            animateRotate: true,
            duration: 1000
        }
    }
});
</script>

{% endblock %}
