{% extends 'base.html' %}
{% load static %}

{% block title %}Vendors List - EEU Contractors{% endblock %}

{% block extra_css %}
<style>
  body {
    font-family: "Poppins", "Times New Roman", serif;
    font-size: 0.9rem;
    background-color: #f4f6f9;
  }

  /* === Header Styling === */
  h2 {
    font-weight: 700;
    letter-spacing: 1px;
    text-align: center;
    background: linear-gradient(to bottom, #ff6600, #28a745);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    font-size: 1.75rem;
    margin-bottom: 1rem;
  }

  .stats-row {
    display: flex;
    flex-wrap: wrap;
    gap: 1rem;
    margin-bottom: 1.5rem;
    justify-content: center;
  }

  .stat-box {
    flex: 1;
    min-width: 140px;
    background-color: #ffffff;
    border: 1px solid #dcdcdc;
    border-left: 3px solid #28a745;
    border-radius: 5px;
    padding: 1rem;
    text-align: center;
  }

  .stat-value {
    font-size: 1.4rem;
    font-weight: 700;
    color: #2c3e50;
  }

  .stat-label {
    font-size: 0.85rem;
    color: #6c757d;
  }

  /* === Filter Card === */
  .filter-card {
    background-color: #ffffff;
    border: 1px solid #dcdcdc;
    border-left: 3px solid #ff6600;
    border-radius: 5px;
    padding: 1.5rem;
    margin-bottom: 1.5rem;
  }

  .form-label {
    font-weight: 500;
  }

  .form-control,
  .form-select {
    height: 36px;
    padding: 0.25rem 0.75rem;
    font-size: 0.875rem;
    border-radius: 4px;
    border: 1px solid #ff6600;
  }

  /* === Search Input Group === */
  .input-group .form-control {
    border-top-left-radius: 20px;
    border-bottom-left-radius: 20px;
  }

  .input-group .btn {
    height: 36px;
    font-size: 0.875rem;
    border-top-right-radius: 20px;
    border-bottom-right-radius: 20px;
    background: linear-gradient(45deg, #28a745, #56ab2f);
    color: white;
    border: none;
  }

  .input-group .btn:hover {
    background: linear-gradient(45deg, #24913d, #4e9b2d);
  }

  /* === Apply Filter Button === */
  .btn-primary {
    background: linear-gradient(45deg, #28a745, #56ab2f);
    color: white;
    border: none;
  }

  .btn-primary:hover {
    background: linear-gradient(45deg, #24913d, #4e9b2d);
  }

  .btn-outline-secondary {
    color: #333;
    border: 1px solid #ccc;
  }

  .btn-outline-secondary:hover {
    background-color: #eee;
  }

  /* === Table Styling === */
  .vendors-table {
    width: 100%;
    border-collapse: collapse;
    border-radius: 5px;
    overflow: hidden;
    background-color: #fff;
    border: 1px solid #dcdcdc;
  }

  .vendors-table th {
    background-color: #343a40;
    color: #fff;
    padding: 0.75rem 1rem;
    font-size: 0.9rem;
    font-weight: 600;
    text-align: center;
  }

  .vendors-table td {
    padding: 0.75rem 1rem;
    border-bottom: 1px solid #e9ecef;
    vertical-align: middle;
  }

  .vendors-table tr:hover {
    background-color: #fdfdfd;
  }

  .vendor-link {
    font-weight: 600;
    color: #2c3e50;
    text-decoration: none;
  }

  .vendor-link:hover {
    text-decoration: underline;
    color: #28a745;
  }

  /* === Status Badges === */
  .badge-registered {
    background-color: #28a745;
    color: #fff;
    padding: 0.35em 0.65em;
    border-radius: 1rem;
    font-size: 0.75rem;
  }

  .badge-not-registered {
    background-color: #ff6600;
    color: #fff;
    padding: 0.35em 0.65em;
    border-radius: 1rem;
    font-size: 0.75rem;
  }

  /* === Pagination === */
  .pagination .page-item .page-link {
    color: #333;
    border: 1px solid #ccc;
    font-weight: 500;
  }

  .pagination .page-item.active .page-link {
    background-color: #28a745;
    border-color: #28a745;
    color: white;
  }

  .pagination-container {
    display: flex;
    justify-content: center;
    margin-top: 1.5rem;
  }

  .card-footer {
    background-color: #f8f9fa;
    padding: 1rem 1.5rem;
    font-size: 0.875rem;
    border-top: 1px solid #dcdcdc;
  }

  .bi {
    vertical-align: -0.125em;
    color: #ff6600;
  }
</style>
{% endblock %}


{% block content %}
<div class="container py-4">
  <h2 class="mb-4" >
    <i class="bi  me-2"></i>Contractors List
    <small class="text-muted">Manage all registered vendors</small>
  </h2>
  
  <!-- Stats Row -->
  <div class="stats-row">
    <div class="stat-box">
      <div class="stat-value">{{ total_count }}</div>
      <div class="stat-label">Total Vendors</div>
    </div>
    <div class="stat-box">
      <div class="stat-value">{{ contractor_count }}</div>
      <div class="stat-label">Contractors</div>
    </div>
    <div class="stat-box">
      <div class="stat-value">{{ enterprise_count }}</div>
      <div class="stat-label">Enterprises</div>
    </div>
    <div class="stat-box">
      <div class="stat-value">{{ new_enterprise_count }}</div>
      <div class="stat-label">New Enterprises</div>
    </div>
  </div>
  
  <!-- Filter Card -->
  <div class="filter-card mb-4">
    {% comment %} <h5 class="mb-3"><i class="bi bi me-2"></i>Filter Vendors</h5> {% endcomment %}
    <form method="get" action="{% url 'vendors_list' %}">
      <div class="row g-3">
        <div class="col-md-4">
          <label class="form-label">Vendor Type</label>
          <select name="contractor_type" class="form-select">
            <option value="all" {% if contractor_type == 'all' %}selected{% endif %}>All Types</option>
            <option value="contractor" {% if contractor_type == 'contractor' %}selected{% endif %}>Contractors</option>
            <option value="enterprise" {% if contractor_type == 'enterprise' %}selected{% endif %}>Enterprises</option>
            <option value="new_enterprise" {% if contractor_type == 'new_enterprise' %}selected{% endif %}>New Enterprises</option>
          </select>
        </div>
        
        <div class="col-md-4">
          <label class="form-label">Search</label>
          <div class="input-group">
            <input type="text" name="search" class="form-control" placeholder="Search by name, ID, or registrar" value="{{ search_query }}">
            <button type="submit" class="btn btn-primary">
              <i class="bi bi-search"></i>
            </button>
          </div>
        </div>
        
        <div class="col-md-4">
          <label class="form-label">Search</label>
          <div class="input-group">
            <input type="text" name="search" class="form-control" placeholder="Search by name, ID, or registrar" value="{{ search_query }}">
            <button type="submit" class="btn btn-primary">
              <i class="bi bi-search"></i>
            </button>
          </div>
        </div>
      </div>
      
      <div class="mt-3 text-end">
        <button type="submit" class="btn btn-primary">
          <i class="bi bi-filter"></i> Apply Filters
        </button>
        <a href="{% url 'vendors_list' %}" class="btn btn-outline-secondary ms-2">
          <i class="bi bi-x-circle"></i> Clear Filters
        </a>
      </div>
    </form>
  </div>
  
  <!-- Table View -->
  <div class="card">
    <div class="card-body p-0">
      <div class="table-responsive">
        <table class="vendors-table">
          <thead>
            <tr>
              <th>የተመዘገበው ስም</th>
              <th>የኮንትራክተሩ አይነት</th>
              <th>የመወዳደሪያ መለያ ቁጥር</th>
              <th>ክልል</th>
              <th>የመዝጋቢ ሰራተኛው ስም</th>
              <th>የተመዘገበበት ቀን</th>
            </tr>
          </thead>
          <tbody>
            {% for vendor in vendors %}
            <tr>
              <td>
                <a href="{% url 'vendor_detail' vendor_id=vendor.id %}" class="vendor-link">
                  {{ vendor.contractor_name }}
                </a>
              </td>
              <td>{{ vendor.get_contractor_type_display }}</td>
              <td>{{ vendor.identification_number }}</td>
              <td>{{ vendor.region.name }}</td> <!-- NEW: Show Region Name -->
              <td>{{ vendor.registrar_name }}</td>
              <td>{{ vendor.registration_date|date:"M d, Y, g:i a" }}</td>
            </tr>
            {% empty %}
            <tr>
              <td colspan="6" class="text-center py-4">No vendors found matching your criteria.</td>
            </tr>
            {% endfor %}
          </tbody>
        </table>
      </div>
    </div>
    <div class="card-footer d-flex justify-content-between align-items-center">
      <span>{{ total_count }} vendorsregistrations</span>
      
      {% if is_paginated %}
      <nav aria-label="Page navigation">
        <ul class="pagination mb-0">
          {% if page_obj.has_previous %}
          <li class="page-item">
            <a class="page-link" href="?page=1{% if search_query %}&search={{ search_query }}{% endif %}{% if contractor_type != 'all' %}&contractor_type={{ contractor_type }}{% endif %}" aria-label="First">
              <span aria-hidden="true">&laquo;&laquo;</span>
            </a>
          </li>
          <li class="page-item">
            <a class="page-link" href="?page={{ page_obj.previous_page_number }}{% if search_query %}&search={{ search_query }}{% endif %}{% if contractor_type != 'all' %}&contractor_type={{ contractor_type }}{% endif %}" aria-label="Previous">
              <span aria-hidden="true">&laquo;</span>
            </a>
          </li>
          {% endif %}
          
          {% for num in page_obj.paginator.page_range %}
          {% if page_obj.number == num %}
          <li class="page-item active">
            <a class="page-link" href="#">{{ num }}</a>
          </li>
          {% elif num > page_obj.number|add:'-3' and num < page_obj.number|add:'3' %}
          <li class="page-item">
            <a class="page-link" href="?page={{ num }}{% if search_query %}&search={{ search_query }}{% endif %}{% if contractor_type != 'all' %}&contractor_type={{ contractor_type }}{% endif %}">{{ num }}</a>
          </li>
          {% endif %}
          {% endfor %}
          
          {% if page_obj.has_next %}
          <li class="page-item">
            <a class="page-link" href="?page={{ page_obj.next_page_number }}{% if search_query %}&search={{ search_query }}{% endif %}{% if contractor_type != 'all' %}&contractor_type={{ contractor_type }}{% endif %}" aria-label="Next">
              <span aria-hidden="true">&raquo;</span>
            </a>
          </li>
          <li class="page-item">
            <a class="page-link" href="?page={{ page_obj.paginator.num_pages }}{% if search_query %}&search={{ search_query }}{% endif %}{% if contractor_type != 'all' %}&contractor_type={{ contractor_type }}{% endif %}" aria-label="Last">
              <span aria-hidden="true">&raquo;&raquo;</span>
            </a>
          </li>
          {% endif %}
        </ul>
      </nav>
      {% endif %}
    </div>
  </div>
</div>
{% endblock %}


