#!/usr/bin/env python3
"""
Investigate existing manual assessments in the database to understand
why current_progress values differ from our test expectations.
"""

import os
import sys
import django

# Setup Django environment
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'EEU_Contractors_enterprise.settings')
django.setup()

from eeucontractors.models import SubActivityWorkload, SubActivityProgress
from django.utils import timezone

def investigate_existing_assessments():
    """Investigate what manual assessments already exist in the database"""
    
    print("🔍 INVESTIGATING EXISTING MANUAL ASSESSMENTS")
    print("=" * 60)
    
    workloads = SubActivityWorkload.objects.all()[:5]  # Check first 5 workloads
    
    for i, workload in enumerate(workloads, 1):
        print(f"\n--- WORKLOAD {i}: {workload.sub_activity.name} ---")
        print(f"Project: {workload.project_progress.project.name}")
        print(f"Quantity: {workload.completed_quantity}/{workload.quantity} ({workload.quantity_progress_percentage}%)")
        
        # Get all progress records for this workload
        progress_records = workload.progress_records.all().order_by('-end_date')
        
        print(f"\n📊 PROGRESS RECORDS ({progress_records.count()} total):")
        
        if progress_records.exists():
            for j, record in enumerate(progress_records[:3], 1):  # Show first 3
                print(f"   {j}. Date: {record.end_date} | Progress: {record.progress}% | Time Factor: {record.time_factor}")
            
            if progress_records.count() > 3:
                print(f"   ... and {progress_records.count() - 3} more records")
            
            latest = workload.latest_progress
            print(f"\n🎯 LATEST ASSESSMENT:")
            print(f"   Date: {latest.end_date}")
            print(f"   Manual Progress: {latest.progress}%")
            print(f"   Time Factor: {latest.time_factor}")
            
        else:
            print("   No manual assessments found")
        
        print(f"\n📈 CURRENT CALCULATION:")
        print(f"   Quantity Progress: {workload.quantity_progress_percentage}%")
        print(f"   Current Progress: {workload.current_progress}%")
        print(f"   Progress Type: {workload.progress_type}")
        
        # Show validation logic in action
        if workload.progress_type == 'assessment':
            latest = workload.latest_progress
            quantity = workload.quantity_progress_percentage
            manual = latest.progress
            current = workload.current_progress
            
            print(f"\n🔍 VALIDATION LOGIC:")
            print(f"   Manual Assessment: {manual}%")
            print(f"   Quantity Progress: {quantity}%")
            
            if quantity >= 80:
                max_allowed = min(100, quantity + 20)
                min_allowed = quantity
                print(f"   High Quantity Protection: min_allowed = {min_allowed}%")
                print(f"   Result: max({min_allowed}, min({manual}, {max_allowed})) = {current}%")
            else:
                max_allowed = min(100, quantity + 20)
                print(f"   Inflation Prevention: max_allowed = {max_allowed}%")
                print(f"   Result: min({manual}, {max_allowed}) = {current}%")
        
        print("-" * 50)

def show_validation_examples():
    """Show examples of different validation scenarios from real data"""
    
    print(f"\n{'='*60}")
    print("🎯 REAL VALIDATION EXAMPLES FROM YOUR DATA")
    print(f"{'='*60}")
    
    # Find examples of different validation scenarios
    scenarios = {
        'high_quantity_protection': [],
        'inflation_prevention': [],
        'normal_acceptance': []
    }
    
    for workload in SubActivityWorkload.objects.all():
        if workload.progress_type == 'assessment':
            latest = workload.latest_progress
            quantity = workload.quantity_progress_percentage
            manual = latest.progress
            current = workload.current_progress
            
            if quantity >= 80 and current == quantity and manual < quantity:
                scenarios['high_quantity_protection'].append({
                    'workload': workload,
                    'quantity': quantity,
                    'manual': manual,
                    'current': current
                })
            elif quantity < 80 and manual > quantity + 20 and current == quantity + 20:
                scenarios['inflation_prevention'].append({
                    'workload': workload,
                    'quantity': quantity,
                    'manual': manual,
                    'current': current
                })
            elif current == manual:
                scenarios['normal_acceptance'].append({
                    'workload': workload,
                    'quantity': quantity,
                    'manual': manual,
                    'current': current
                })
    
    for scenario_type, examples in scenarios.items():
        print(f"\n🎯 {scenario_type.upper().replace('_', ' ')} EXAMPLES:")
        if examples:
            for example in examples[:2]:  # Show first 2 examples
                w = example['workload']
                print(f"   • {w.sub_activity.name}: Qty={example['quantity']}%, Manual={example['manual']}%, Current={example['current']}%")
        else:
            print("   No examples found in current data")

if __name__ == "__main__":
    investigate_existing_assessments()
    show_validation_examples()
