{% extends "base.html" %}
{% load static %}

{% block content %}
<div class="container py-4">
  <div class="row justify-content-center">
    <div class="col-lg-8">
      <div class="card shadow-sm">
        <div class="card-header bg-success text-white">
          <h5 class="mb-0">
            <i class="bi bi-clipboard-check me-2"></i>{{ title }}
          </h5>
        </div>
        
        <div class="card-body">
          <form method="post">
            {% csrf_token %}
            
            {% if form.non_field_errors %}
              <div class="alert alert-danger">
                {{ form.non_field_errors }}
              </div>
            {% endif %}
            
            <div class="row">
              <!-- Region and Main Activity -->
              <div class="col-md-6 mb-3">
                <label for="{{ form.region.id_for_label }}" class="form-label">
                  {{ form.region.label }}
                </label>
                {{ form.region }}
                {% if form.region.errors %}
                  <div class="text-danger small">{{ form.region.errors }}</div>
                {% endif %}
              </div>
              
              <div class="col-md-6 mb-3">
                <label for="{{ form.main_activity.id_for_label }}" class="form-label">
                  {{ form.main_activity.label }}
                </label>
                {{ form.main_activity }}
                {% if form.main_activity.errors %}
                  <div class="text-danger small">{{ form.main_activity.errors }}</div>
                {% endif %}
              </div>
            </div>
            
            <!-- Availability and Vendor Assignment -->
            <div class="row">
              <div class="col-md-6 mb-3">
                <div class="form-check">
                  {{ form.is_available }}
                  <label class="form-check-label" for="{{ form.is_available.id_for_label }}">
                    {{ form.is_available.label }}
                  </label>
                </div>
                {% if form.is_available.errors %}
                  <div class="text-danger small">{{ form.is_available.errors }}</div>
                {% endif %}
              </div>
              
              <div class="col-md-6 mb-3">
                <label for="{{ form.assigned_vendor.id_for_label }}" class="form-label">
                  {{ form.assigned_vendor.label }}
                </label>
                {{ form.assigned_vendor }}
                {% if form.assigned_vendor.errors %}
                  <div class="text-danger small">{{ form.assigned_vendor.errors }}</div>
                {% endif %}
              </div>
            </div>
            
            <!-- Project Assignment -->
            <div class="mb-3">
              <label for="{{ form.project.id_for_label }}" class="form-label">
                {{ form.project.label }}
              </label>
              {{ form.project }}
              {% if form.project.errors %}
                <div class="text-danger small">{{ form.project.errors }}</div>
              {% endif %}
            </div>
            
            <!-- Director Approval Section -->
            <div class="card mb-3">
              <div class="card-header bg-light">
                <h6 class="mb-0">Director Approval</h6>
              </div>
              <div class="card-body">
                <div class="row">
                  <div class="col-md-6 mb-3">
                    <div class="form-check">
                      {{ form.approved_by_director }}
                      <label class="form-check-label" for="{{ form.approved_by_director.id_for_label }}">
                        {{ form.approved_by_director.label }}
                      </label>
                    </div>
                    {% if form.approved_by_director.errors %}
                      <div class="text-danger small">{{ form.approved_by_director.errors }}</div>
                    {% endif %}
                  </div>
                  
                  <div class="col-md-6 mb-3">
                    <label for="{{ form.director_approval_date.id_for_label }}" class="form-label">
                      {{ form.director_approval_date.label }}
                    </label>
                    {{ form.director_approval_date }}
                    {% if form.director_approval_date.errors %}
                      <div class="text-danger small">{{ form.director_approval_date.errors }}</div>
                    {% endif %}
                  </div>
                </div>
                
                <div class="mb-3">
                  <label for="{{ form.director_notes.id_for_label }}" class="form-label">
                    {{ form.director_notes.label }}
                  </label>
                  {{ form.director_notes }}
                  {% if form.director_notes.errors %}
                    <div class="text-danger small">{{ form.director_notes.errors }}</div>
                  {% endif %}
                </div>
              </div>
            </div>
            
            <!-- Form Actions -->
            <div class="d-flex justify-content-between">
              <a href="{% url 'regional_template_list' %}" class="btn btn-secondary">
                <i class="bi bi-arrow-left me-1"></i>Back to List
              </a>
              <button type="submit" class="btn btn-success">
                <i class="bi bi-check-circle me-1"></i>Save Template
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>
  </div>
</div>
{% endblock %}
