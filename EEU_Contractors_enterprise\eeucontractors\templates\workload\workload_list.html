{% extends 'base.html' %}
{% load static %}

{% block content %}
<div class="container mt-4">
  <div class="d-flex justify-content-between align-items-center mb-4">
    <div>
      <h2>Workloads for {% if workloads %}{{ workloads.0.project_progress.project.name }}{% else %}Project{% endif %}</h2>
    </div>
    <div>
      <a href="{% url 'project_progress_list' %}" class="btn btn-secondary me-2">
        <i class="bi bi-arrow-left"></i> Back to Projects
      </a>
      <a href="{% url 'add_workload' progress_id=progress_id %}" class="btn btn-warning">
        <i class="bi bi-plus-circle"></i> Add Workload
      </a>
    </div>
  </div>



  {% include 'components/messages.html' %}



  {% if workloads %}
    <div class="table-responsive">
      <table class="table table-bordered bg-white">
        <thead class="bg-light">
          <tr>
            <th>Sub Activity</th>
            <th>Quantity</th>
            <th>Unit Type</th>
            <th>Expected Duration (days)</th>
            <th>Progress</th>
            <th>Actions</th>
          </tr>
        </thead>
        <tbody>
          {% for workload in workloads %}
            <tr>
              <td>{{ workload.sub_activity.name }}</td>
              <td>{{ workload.quantity|default:0 }}</td>
              <td>{{ workload.unit_type|default:"units" }}</td>
              <td>{{ workload.expected_duration|default:"Not set" }}</td>
              <td>
                <div class="progress mb-1" style="height: 20px;">
                  <div class="progress-bar bg-primary"
                       role="progressbar"
                       style="width: {{ workload.current_progress }}%;"
                       data-progress="{{ workload.current_progress }}">
                    {{ workload.current_progress }}%
                  </div>
                </div>
                {% if workload.progress_type == 'assessment' %}
                  <small class="text-muted">
                    <i class="fas fa-user-check text-info"></i> Assessment-based
                    {% if workload.quantity_progress_percentage != workload.current_progress %}
                      (Qty: {{ workload.quantity_progress_percentage }}%)
                    {% endif %}
                  </small>
                {% else %}
                  <small class="text-muted">
                    <i class="fas fa-calculator text-secondary"></i> Quantity-based
                  </small>
                {% endif %}
              </td>
              <td>
                <div class="btn-group" role="group">
                  <a href="{% url 'workload_detail' workload_id=workload.id %}" class="btn btn-sm btn-outline-warning" title="View Details">
                    <i class="bi bi-eye"></i>
                  </a>
                  <a href="{% url 'edit_workload' workload_id=workload.id %}" class="btn btn-sm btn-outline-secondary" title="Edit Workload">
                    <i class="bi bi-pencil"></i>
                  </a>
                </div>
              </td>
            </tr>
          {% endfor %}
        </tbody>
      </table>
    </div>
  {% else %}
    <div class="alert alert-info">
      No workloads found for this project progress. Click "Add Workload" to create one.
    </div>
  {% endif %}
</div>

<script>
// Set progress bar widths dynamically to avoid Django template syntax in CSS
document.addEventListener('DOMContentLoaded', function() {
  const progressBars = document.querySelectorAll('.progress-bar[data-progress]');
  progressBars.forEach(function(bar) {
    const progress = bar.getAttribute('data-progress');
    bar.style.width = progress + '%';
  });
});
</script>

{% endblock %}













