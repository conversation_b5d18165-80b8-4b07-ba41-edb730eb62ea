from django import forms
from .models import  Vendorsregistration,Project,Vendor,Region,SubActivityWorkload,SubActivityProgress, ProjectVendorsHistory,VendorDocument, SubActivity
from django.forms import DateInput

class VendorForm(forms.ModelForm):
    class Meta:
        model = Vendor
        fields = ['vendor_registration', 'phone_number', 'year', 'experience']
    def clean_vendor_registration(self):
        vendor_registration = self.cleaned_data['vendor_registration']
        VendorModel = Vendor
        # Check if another vendor already registered with this vendor_registration
        if VendorModel.objects.filter(vendor_registration=vendor_registration).exists():
            raise forms.ValidationError("A vendor is already registered with this vendor registration.")
      # Check if there's an active project for this vendor_registration
        from .models import Project
        if Project.objects.filter(vendor__vendor_registration=vendor_registration, status='active').exists():
            raise forms.ValidationError("An active project is already associated with this vendor registration.")
        return vendor_registration
class VendorsRegistrationForm(forms.ModelForm):
    class Meta:
        model = Vendorsregistration
        fields = [
            'region',  # 👈 Include region here
            'contractor_type', 'contractor_name', # Added new field
            'has_professional_license', 'electric_grade', 'has_human_resource',
            'electrical_engineers', 'electromechanical_engineers', 'civil_engineers',
            'electricians_foremen', 'surveyors', 'line_workers',
            'has_work_experience_doc', 'has_trade_license', 'has_energy_certification',
            'has_equipment_resources', 'has_vat_and_tax', 'has_good_performance',
            'has_financial_capacity', 'is_registered', 'registrar_name', 'registrar_id'
        ]
        # identification_number and created_at are excluded because they're auto-populated
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)      
        # Set default contractor_type to 'new_enterprise'
        self.fields['contractor_type'].initial = 'new_enterprise'
                # Make some fields required
        self.fields['registrar_name'].required = True
        self.fields['registrar_id'].required = True
                # Add Bootstrap classes to all fields
        for field_name, field in self.fields.items():
            if isinstance(field.widget, forms.CheckboxInput):
                field.widget.attrs['class'] = 'form-check-input'
            else:
                field.widget.attrs['class'] = 'form-control'
                # Add placeholder text
            if field_name == 'contractor_name':
                field.widget.attrs['placeholder'] = 'Enter contractor name'
            elif field_name == 'registrar_name':
                field.widget.attrs['placeholder'] = 'Enter your name'
            elif field_name == 'registrar_id':
                field.widget.attrs['placeholder'] = 'Enter your ID'    
        # Add help text for some fields
        self.fields['electric_grade'].help_text = 'Select the appropriate electric grade level'
        self.fields['has_human_resource'].help_text = 'Check if all required human resources are available'
            # Group number fields
        for field_name in ['electrical_engineers', 'electromechanical_engineers', 'civil_engineers',
                          'electricians_foremen', 'surveyors', 'line_workers']:
            self.fields[field_name].widget.attrs['min'] = 0
    def clean(self):
        cleaned_data = super().clean()     
        # Validate that if has_human_resource is True, at least one engineer type has a value > 0
        has_human_resource = cleaned_data.get('has_human_resource')
        if has_human_resource:
            engineers_count = (
                cleaned_data.get('electrical_engineers', 0) +
                cleaned_data.get('electromechanical_engineers', 0) +
                cleaned_data.get('civil_engineers', 0)
            )
    
            if engineers_count == 0:
                raise forms.ValidationError(
                    "If human resources are available, at least one engineer type must be specified."
                )
                
        # Validate electric_grade is provided if has_professional_license is True
        has_professional_license = cleaned_data.get('has_professional_license')
        electric_grade = cleaned_data.get('electric_grade')     
        if has_professional_license and not electric_grade:
            self.add_error(
                'electric_grade',
                "Electric grade is required when professional license is available."
            )
            
        return cleaned_data
# Add this new form for advanced filtering
class VendorFilterForm(forms.Form):
    CONTRACTOR_TYPE_CHOICES = [
        ('all', 'All Types'),
        ('contractor', 'Contractor'),
        ('enterprise', 'Enterprise'),
        ('new_enterprise', 'New Enterprise'),
    ]
    REGISTRATION_STATUS_CHOICES = [
        ('All', 'All Status'),
        ('Yes', 'Registered'),
        ('No', 'Not Registered'),
    ]
    
    DATE_FILTER_CHOICES = [
        ('any', 'Any Time'),
        ('today', 'Today'),
        ('this_week', 'This Week'),
        ('this_month', 'This Month'),
        ('custom', 'Custom Range'),
    ]
    
    contractor_type = forms.ChoiceField(
        choices=CONTRACTOR_TYPE_CHOICES,
        required=False,
        widget=forms.Select(attrs={'class': 'form-select'})
    )
    
    is_registered = forms.ChoiceField(
        choices=REGISTRATION_STATUS_CHOICES,
        required=False,
        widget=forms.Select(attrs={'class': 'form-select'})
    )
    
    date_filter = forms.ChoiceField(
        choices=DATE_FILTER_CHOICES,
        required=False,
        widget=forms.Select(attrs={'class': 'form-select'})
    )
    
    start_date = forms.DateField(
        required=False,
        widget=forms.DateInput(attrs={'class': 'form-control', 'type': 'date'})
    )
    
    end_date = forms.DateField(
        required=False,
        widget=forms.DateInput(attrs={'class': 'form-control', 'type': 'date'})
    )
    
    search = forms.CharField(
        required=False,
        widget=forms.TextInput(attrs={
            'class': 'form-control',
            'placeholder': 'Search by name, ID or registrar'
        })
    )
    
    def clean(self):
        cleaned_data = super().clean()
        date_filter = cleaned_data.get('date_filter')
        start_date = cleaned_data.get('start_date')
        end_date = cleaned_data.get('end_date')
        
        if date_filter == 'custom':
            if not start_date:
                self.add_error('start_date', 'Start date is required for custom date range')
            if not end_date:
                self.add_error('end_date', 'End date is required for custom date range')
            if start_date and end_date and start_date > end_date:
                self.add_error('end_date', 'End date must be after start date')
                
        return cleaned_data
class ProjectForm(forms.ModelForm):
    class Meta:
        model = Project
        fields = '__all__'
        widgets = {
            'start_date': DateInput(attrs={'type': 'date'}),
            'estimated_end_date': DateInput(attrs={'type': 'date'}),
            'end_date': DateInput(attrs={'type': 'date'}),
            'approved_date': DateInput(attrs={'type': 'date'}),
        }

    def __init__(self, *args, **kwargs):
        user = kwargs.pop('user', None)
        super().__init__(*args, **kwargs)
        
        for field in self.fields.values():
            field.widget.attrs.update({'class': 'form-control'})

        self.fields['level'].help_text = 'Level must match vendor capabilities and activity requirements.'
        self.fields['main_activity'].help_text = 'Select the primary activity for this project.'

        if self.instance and self.instance.vendor_id:
            try:
                vendor = self.instance.vendor
                self.fields['level'].initial = vendor.level
            except Exception:
                pass

        if user and not user.is_superuser:
            self.fields['region'].queryset = Region.objects.filter(id=user.region_id)

    def clean(self):
        cleaned_data = super().clean()
        vendor = cleaned_data.get('vendor')
        level = cleaned_data.get('level')
        main_activity = cleaned_data.get('main_activity')

        if vendor and level:
            if str(vendor.level) != str(level):
                self.add_error(
                    'level',
                    f"Vendor level in form ({level}) doesn't match vendor's actual level ({vendor.level})."
                )

        if main_activity and level:
            try:
                level_int = int(level)
            except (ValueError, TypeError):
                self.add_error('level', "Invalid level format.")
                return cleaned_data

            required_levels = main_activity.get_required_levels()

            level_map = {
                'level_1_2_3': {1, 2, 3},
                'level_4_5_6': {4, 5, 6},
                'level_7_8': {7, 8},
            }

            allowed_levels = set()
            for group in required_levels:
                allowed_levels.update(level_map.get(group, set()))

            if level_int not in allowed_levels:
                self.add_error(
                    'main_activity',
                    f"Vendor level {level_int} is not suitable for this activity. Allowed levels: {sorted(allowed_levels)}."
                )
        return cleaned_data
from django import forms
from django.forms import inlineformset_factory, DateInput
from .models import (
    SubActivityWorkload, SubActivityProgress, ProjectVendorsHistory,
    VendorDocument, SubActivity, ProjectProgress
)
# Date picker widget
class DatePickerInput(DateInput):
    input_type = 'date'

# ProjectProgress Form
class ProjectProgressForm(forms.ModelForm):
    class Meta:
        model = ProjectProgress
        fields = ['project', 'vendor', 'main_activity', 'start_date', 'progress_percentage', 'completion_date', 'status', 'remarks']
        widgets = {
            'start_date': DatePickerInput(),
            'completion_date': DatePickerInput(),
            'remarks': forms.Textarea(attrs={'rows': 3}),
        }

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        for visible in self.visible_fields():
            visible.field.widget.attrs['class'] = 'form-control'

        # Add help text
        self.fields['progress_percentage'].help_text = 'Overall progress percentage (0-100)'
        self.fields['status'].help_text = 'Current status of the project'

    def clean(self):
        cleaned_data = super().clean()
        start_date = cleaned_data.get('start_date')
        completion_date = cleaned_data.get('completion_date')
        progress_percentage = cleaned_data.get('progress_percentage')
        status = cleaned_data.get('status')
        project = cleaned_data.get('project')
        vendor = cleaned_data.get('vendor')
        main_activity = cleaned_data.get('main_activity')

        # Validate date logic
        if start_date and completion_date and start_date > completion_date:
            raise forms.ValidationError("Completion date cannot be earlier than start date.")

        # Validate progress percentage
        if progress_percentage is not None:
            if progress_percentage < 0 or progress_percentage > 100:
                raise forms.ValidationError("Progress percentage must be between 0 and 100.")

        # Validate completion logic
        if status == 'completed':
            if not completion_date:
                raise forms.ValidationError("Completion date is required when status is 'completed'.")
            if progress_percentage and progress_percentage < 100:
                raise forms.ValidationError("Progress must be 100% when status is 'completed'.")

        # Validate vendor and project relationship
        if project and vendor:
            if project.vendor != vendor:
                raise forms.ValidationError("Selected vendor must match the project's assigned vendor.")

        # Validate main activity compatibility
        if project and main_activity:
            if project.main_activity != main_activity:
                raise forms.ValidationError("Main activity must match the project's main activity.")

        return cleaned_data
class SubActivityWorkloadForm(forms.ModelForm):
    class Meta:
        model = SubActivityWorkload
        fields = ['sub_activity', 'quantity']
        widgets = {
            'sub_activity': forms.Select(attrs={'class': 'form-control'}),
            'quantity': forms.NumberInput(attrs={'class': 'form-control', 'min': '1'})
        }
    
    def __init__(self, *args, **kwargs):
        project_progress = kwargs.pop('project_progress', None)
        super().__init__(*args, **kwargs)
        if project_progress:
            # Filter sub_activity choices based on the main_activity of the project_progress
            if project_progress.main_activity:
                self.fields['sub_activity'].queryset = self.fields['sub_activity'].queryset.filter(
                    main_activity=project_progress.main_activity
                )

class SubActivityProgressForm(forms.ModelForm):
    class Meta:
        model = SubActivityProgress
        fields = ['progress', 'start_date', 'end_date']
        widgets = {
            'progress': forms.NumberInput(attrs={
                'class': 'form-control',
                'min': '0',
                'max': '100',
                'required': 'required',
                'placeholder': 'Enter progress %'
            }),
            'start_date': forms.DateInput(attrs={
                'class': 'form-control',
                'type': 'date',
                'required': 'required'
            }),
            'end_date': forms.DateInput(attrs={
                'class': 'form-control',
                'type': 'date',
                'required': 'required'
            }),
        }

    def clean(self):
        cleaned_data = super().clean()
        start_date = cleaned_data.get('start_date')
        end_date = cleaned_data.get('end_date')
        progress = cleaned_data.get('progress')

        # Validate date logic
        if start_date and end_date and start_date > end_date:
            raise forms.ValidationError("End date cannot be earlier than start date.")

        # Validate progress is reasonable
        if progress is not None:
            if progress < 0 or progress > 100:
                raise forms.ValidationError("Progress must be between 0 and 100.")

        return cleaned_data

# Define the formset for SubActivityProgress
SubActivityProgressFormSet = inlineformset_factory(
    SubActivityWorkload,  # parent model
    SubActivityProgress,  # child model
    form=SubActivityProgressForm,
    fields=('progress', 'start_date', 'end_date'),
    extra=1,
    can_delete=True,
    fk_name='workload'  # Explicitly specify the foreign key field name
)
class ProjectVendorsHistoryForm(forms.ModelForm):
    class Meta:
        model = ProjectVendorsHistory
        fields = [
            'vendor', 'project', 'status', 'evaluation_notes', 
            'start_date', 'end_date', 'cause_of_delay_or_suspension'
        ]
        widgets = {
            'vendor': forms.Select(attrs={'class': 'form-control'}),
            'project': forms.Select(attrs={'class': 'form-control'}),
            'status': forms.Select(attrs={'class': 'form-control'}),
            'evaluation_notes': forms.Textarea(attrs={'class': 'form-control', 'rows': 3}),
            'start_date': DatePickerInput(attrs={'class': 'form-control'}),
            'end_date': DatePickerInput(attrs={'class': 'form-control'}),
            'cause_of_delay_or_suspension': forms.Select(attrs={'class': 'form-control'}),
        }
    
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        # Make cause_of_delay_or_suspension initially hidden
        self.fields['cause_of_delay_or_suspension'].widget.attrs['class'] += ' delay-reason-field'
        self.fields['cause_of_delay_or_suspension'].widget.attrs['id'] = 'delay_reason_field'

# forms.py
from django import forms
from .models import VendorDocument

class VendorDocumentForm(forms.ModelForm):
    vendor_id_number = forms.CharField(required=False, label="ID Number", widget=forms.TextInput(attrs={'readonly': 'readonly'}))
    vendor_phone = forms.CharField(required=False, label="Phone", widget=forms.TextInput(attrs={'readonly': 'readonly'}))
    vendor_level = forms.CharField(required=False, label="Level", widget=forms.TextInput(attrs={'readonly': 'readonly'}))
    vendor_type = forms.CharField(required=False, label="Type", widget=forms.TextInput(attrs={'readonly': 'readonly'}))

    class Meta:
        model = VendorDocument
        fields = ['vendor', 'vendor_id_number', 'vendor_phone', 'vendor_level', 'vendor_type', 'document_type', 'file', 'description', 'expiry_date']
        widgets = {
            'expiry_date': forms.DateInput(attrs={'type': 'date', 'class': 'form-control'}),
        }


from django import forms
from .models import RegionalPerformance

class RegionalPerformanceForm(forms.ModelForm):
    class Meta:
        model = RegionalPerformance
        fields = '__all__'
        widgets = {
            'last_updated': forms.DateTimeInput(attrs={'readonly': 'readonly'}),
        }
    
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        # Example: make all fields readonly except for total_projects (if you want)
        for field_name, field in self.fields.items():
            if field_name != 'total_projects':
                field.widget.attrs['readonly'] = True
                field.disabled = True
from django import forms
from .models import RegionalProjectTemplate

class RegionalProjectTemplateForm(forms.ModelForm):
    class Meta:
        model = RegionalProjectTemplate
        fields = [
            'region', 'main_activity', 'is_available',
            'assigned_vendor', 'project',
            'approved_by_director', 'director_approval_date', 'director_notes'
        ]
        widgets = {
            'director_approval_date': forms.DateTimeInput(attrs={'type': 'datetime-local'}),
            'director_notes': forms.Textarea(attrs={'rows': 3}),
        }

    def clean(self):
        cleaned_data = super().clean()
        assigned_vendor = cleaned_data.get('assigned_vendor')
        project = cleaned_data.get('project')

        if assigned_vendor and project and project.vendor != assigned_vendor:
            raise forms.ValidationError("Assigned vendor must match the project vendor.")

        # You can add additional custom validation here if needed
        return cleaned_data
