{% extends 'base.html' %}
{% load static %}
{% block title %}Projects List{% endblock %}
{% block content %}
<style>
  
  /* Cleaner and modern input/select appearance */
  .form-control,
  .form-select {
    background-color: #fdfdfd; /* neutral light background */
    border: 1px solid #ddd;    /* subtle border */
    border-radius: 10px;
    padding: 6px 12px;
    font-size: 0.93rem;
    transition: all 0.2s ease-in-out;
    color: #212529;
  }

  .form-control::placeholder {
    color: #999;
  }
  <style>
  /* Table header style with soft modern orange-green gradient */
  .table thead th {
    background: linear-gradient(to right, #fddc9b, #c3e6cb); /* soft orange to green */
    color: #333;
    font-weight: 600;
    font-size: 0.9rem;
    text-transform: uppercase;
    border-bottom: 2px solid #e0e0e0;
    letter-spacing: 0.5px;
  }

  /* Hover effect for table rows */
  .table tbody tr:hover {
    background-color: #fffdf5;
  }

  /* General table body cell style */
  .table td {
    font-size: 0.9rem;
    vertical-align: middle;
    padding: 0.6rem;
  }

  /* Optional: rounded corners at top of table */
  .table thead th:first-child {
    border-top-left-radius: 10px;
  }

  .table thead th:last-child {
    border-top-right-radius: 10px;
  }


  /* Focus effect */
  .form-control:focus,
  .form-select:focus {
    background-color: #ffffff;
    border-color: #2e8b57; /* soft green border on focus */
    box-shadow: 0 0 5px rgba(46, 139, 87, 0.2);
    outline: none;
  }

  .form-label {
    font-weight: 600;
    font-size: 0.85rem;
    color: #333;
  }

  /* Small spacing between filter fields */
  .filter-card input,
  .filter-card select {
    margin-bottom: 5px;
  }
  /* Title styling with green gradient */
  h2 {
    font-family: "Times New Roman", serif;
    letter-spacing: 2px;
    background: linear-gradient(to bottom, #2e8b57, #1b5e20);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    font-weight: bold;
  }

  /* Heart-style filter box */
  .filter-card {
    background: linear-gradient(145deg, #e5f7e8, #f4fff4); /* soft green */
    border-left: 5px solid #2e8b57;
    border-radius: 20px;
    box-shadow: 0 4px 8px rgba(46, 139, 87, 0.1);
    padding: 20px;
  }

  /* Shorter and styled filter inputs */
  .form-select, .form-control {
    height: 35px;
    border-radius: 10px;
    border: 1.5px solid #2e8b57;
    background-color: #f7fdf9;
  }

  .form-select:focus, .form-control:focus {
    box-shadow: 0 0 5px rgba(46, 139, 87, 0.4);
    border-color: #45a049;
    background-color: #ffffff;
  }

  /* Distinct buttons with glowing hover */
  .btn-primary {
    background-color: #2e8b57;
    border: none;
    border-radius: 20px;
    padding: 6px 20px;
    font-weight: 600;
    box-shadow: 0 2px 4px rgba(46, 139, 87, 0.4);
    transition: all 0.3s ease-in-out;
  }

  .btn-primary:hover {
    background-color: #45a049;
    box-shadow: 0 0 10px rgba(46, 139, 87, 0.7);
  }

  .btn-outline-secondary {
    color: #f2a847;
    border: 2px solid #f2a847;
    border-radius: 20px;
    transition: 0.3s ease-in-out;
  }

  .btn-outline-secondary:hover {
    background-color: #f2a847;
    color: white;
    border-color: #2e8b57;
  }

  /* Table header field styles */
.table thead th {
    {% comment %} background-color: #89CFF0;   {% endcomment %}
    font-weight: bold;
    font-size: 0.95rem;
    color: #003f5c;
    background-color:gradient rgb(44, 62, 80)
    text-transform:capitalize;;
    border: none;
  } 

  .table tbody tr:hover {
    background-color: #fffbe6 !important;
  }
{% comment %} 
 /* Badges refinement */
  .badge {
    font-size: 0.75rem;
    padding: 0.4em 0.6em;
    border-radius: 0.5rem;
  }

  .badge.bg-success {
    background-color: #2e8b57 !important;
  }

  .badge.bg-warning {
    background-color: #f2a847 !important;
    color: #3e3e1f;
  }

  .badge.bg-secondary {
    background-color: #999;
  }  {% endcomment %}
</style>

<div class="container mt-4">
  <div class="d-flex justify-content-between align-items-center mb-4">
    <h2>Projects</h2>
    {% if perms.eeucontractors.add_project %}
    <a href="{% url 'project_create' %}" class="btn btn-primary">
      <i class="bi bi-plus-circle"></i> Add New Project
    </a>
    {% endif %}
  </div>

  <!-- Heart-style Filter Box -->
  <div class="filter-card mb-4">
    <form method="get" class="row g-3">
      <div class="col-md-3">
        <label for="status" class="form-label">Status</label>
        <select name="status" id="status" class="form-select">
          <option value="">All Statuses</option>
          {% for status_value, status_label in status_choices %}
          <option value="{{ status_value }}" {% if selected_status == status_value %}selected{% endif %}>
            {{ status_label }}
          </option>
          {% endfor %}
        </select>
      </div>
      <div class="col-md-3">
        <label for="region" class="form-label">Region</label>
        <select name="region" id="region" class="form-select">
          <option value="">All Regions</option>
          {% for region in regions %}
          <option value="{{ region.name }}" {% if selected_region == region.name %}selected{% endif %}>
            {{ region }}
          </option>
          {% endfor %}
        </select>
      </div>
      <div class="col-md-3">
        <label for="type" class="form-label">Type</label>
        <select name="type" id="type" class="form-select">
          <option value="">All Types</option>
          {% for type_value, type_label in type_choices %}
          <option value="{{ type_value }}" {% if selected_type == type_value %}selected{% endif %}>
            {{ type_label }}
          </option>
          {% endfor %}
        </select>
      </div>
      <div class="col-md-3">
        <label for="search" class="form-label">Search</label>
        <input type="text" name="search" id="search" class="form-control"
               placeholder="Project or vendor name" value="{{ search_query }}">
      </div>
      <div class="col-12 d-flex justify-content-between">
        <button type="submit" class="btn btn-primary">
          <i class="bi bi-filter-circle"></i> Apply Filters
        </button>
        <a href="{% url 'project_list' %}" class="btn btn-outline-secondary">
          <i class="bi bi-x-circle"></i> Clear
        </a>
      </div>
    </form>
  </div>

  <!-- Project Table -->
  {% if projects %}
  <div class="table-responsive">
    <table class="table table-striped table-hover">
      <thead>
        <tr>
          <th>Project Name</th>
          <th>Vendor</th>
          <th>Region</th>
          <th>Type</th>
          <th>Status</th>
          <th>Start Date</th>
          <th>End Date</th>
          <th>Approved</th>
          <th>Actions</th>
        </tr>
      </thead>
      <tbody>
        {% for project in projects %}
        <tr>
          <td>{{ project.name }}</td>
          <td>{{ project.vendor.name }}</td>
          <td>{{ project.region }}</td>
          <td>{{ project.get_type_display }}</td>
          <td>
            <span class="badge {% if project.status == 'active' %}bg-success
                  {% elif project.status == 'suspended' %}bg-warning
                  {% else %}bg-secondary{% endif %}">
              {{ project.get_status_display }}
            </span>
          </td>
          <td>{{ project.start_date }}</td>
          <td>{{ project.end_date }}</td>
          <td>
            {% if project.approved %}
            <span class="badge bg-success">Approved</span>
            {% else %}
            <span class="badge bg-secondary">Pending</span>
            {% endif %}
          </td>
          <td>
            <div class="btn-group">
              {% if perms.eeucontractors.change_project %}
              <a href="{% url 'project_edit' project.id %}" class="btn btn-sm btn-outline-secondary" title="Edit">
                <i class="bi bi-pencil"></i>
              </a>
              {% endif %}
            </div>
          </td>
        </tr>
        {% endfor %}
      </tbody>
    </table>
  </div>

  <!-- Pagination -->
  {% if is_paginated %}
  <nav aria-label="Page navigation">
    <ul class="pagination justify-content-center">
      {% if page_obj.has_previous %}
      <li class="page-item">
        <a class="page-link" href="?page=1{% for key, value in request.GET.items %}{% if key != 'page' %}&{{ key }}={{ value }}{% endif %}{% endfor %}">First</a>
      </li>
      <li class="page-item">
        <a class="page-link" href="?page={{ page_obj.previous_page_number }}{% for key, value in request.GET.items %}{% if key != 'page' %}&{{ key }}={{ value }}{% endif %}{% endfor %}">Previous</a>
      </li>
      {% endif %}
      <li class="page-item active">
        <span class="page-link">Page {{ page_obj.number }} of {{ page_obj.paginator.num_pages }}</span>
      </li>
      {% if page_obj.has_next %}
      <li class="page-item">
        <a class="page-link" href="?page={{ page_obj.next_page_number }}{% for key, value in request.GET.items %}{% if key != 'page' %}&{{ key }}={{ value }}{% endif %}{% endfor %}">Next</a>
      </li>
      <li class="page-item">
        <a class="page-link" href="?page={{ page_obj.paginator.num_pages }}{% for key, value in request.GET.items %}{% if key != 'page' %}&{{ key }}={{ value }}{% endif %}{% endfor %}">Last</a>
      </li>
      {% endif %}
    </ul>
  </nav>
  {% endif %}
  {% else %}
  <div class="alert alert-info mt-3">
    <i class="bi bi-info-circle"></i> No projects found matching your criteria.
  </div>
  {% endif %} 
</div>
{% endblock %}
