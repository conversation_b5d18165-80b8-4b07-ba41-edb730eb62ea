# Generated by Django 5.2.3 on 2025-07-01 07:53

import django.core.validators
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('eeucontractors', '0002_alter_project_region'),
    ]

    operations = [
        migrations.AlterField(
            model_name='project',
            name='status',
            field=models.CharField(choices=[('active', 'active'), ('completed', 'Completed'), ('delayed', 'Delayed'), ('suspended', 'Suspended'), ('cancelled', 'Cancelled'), ('completed with delay', 'Completed with delay')], max_length=20),
        ),
        migrations.AlterField(
            model_name='projectprogress',
            name='status',
            field=models.CharField(choices=[('active', 'active'), ('completed', 'Completed'), ('delayed', 'Delayed'), ('suspended', 'Suspended'), ('cancelled', 'Cancelled'), ('completed with delay', 'Completed with delay')], max_length=30),
        ),
        migrations.Alter<PERSON>ield(
            model_name='subactivityworkload',
            name='expected_duration',
            field=models.PositiveIntegerField(default=1, help_text='Expected duration in days (auto-calculated: base_duration × quantity)', validators=[django.core.validators.MinValueValidator(1)]),
        ),
        migrations.AlterField(
            model_name='vendor',
            name='status',
            field=models.CharField(choices=[('active', 'Active'), ('inactive', 'Inactive'), ('blacklisted', 'Blacklisted')], default='inactive', editable=False, max_length=15),
        ),
    ]
