{% extends 'base.html' %}
{% load static %}

{% block title %}Vendor Management{% endblock %}

{% block content %}
<div class="container-fluid mt-4">
  <div class="d-flex justify-content-between align-items-center mb-4">
    <h2><i class="bi bi-gear me-2"></i>Vendor Management</h2>
    <div>
      <a href="{% url 'available_vendors_list' %}" class="btn btn-success">
        <i class="bi bi-person-check"></i> Available Vendors
      </a>
      <a href="{% url 'vendors_registration' %}" class="btn btn-primary">
        <i class="bi bi-plus-circle"></i> Register New Vendor
      </a>
    </div>
  </div>

  <!-- Statistics Cards -->
  <div class="row mb-4">
    <div class="col-md-3">
      <div class="card bg-primary text-white">
        <div class="card-body">
          <div class="d-flex justify-content-between">
            <div>
              <h4>{{ total_vendors }}</h4>
              <p class="mb-0">Total Vendors</p>
            </div>
            <div class="align-self-center">
              <i class="bi bi-building fs-1"></i>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="col-md-3">
      <div class="card bg-success text-white">
        <div class="card-body">
          <div class="d-flex justify-content-between">
            <div>
              <h4>{{ active_vendors }}</h4>
              <p class="mb-0">Active Vendors</p>
            </div>
            <div class="align-self-center">
              <i class="bi bi-play-circle fs-1"></i>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="col-md-3">
      <div class="card bg-info text-white">
        <div class="card-body">
          <div class="d-flex justify-content-between">
            <div>
              <h4>{{ available_vendors }}</h4>
              <p class="mb-0">Available Vendors</p>
            </div>
            <div class="align-self-center">
              <i class="bi bi-person-check fs-1"></i>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="col-md-3">
      <div class="card bg-warning text-white">
        <div class="card-body">
          <div class="d-flex justify-content-between">
            <div>
              <h4>{{ total_vendors|add:"-"|add:active_vendors|add:"-"|add:available_vendors }}</h4>
              <p class="mb-0">Other Status</p>
            </div>
            <div class="align-self-center">
              <i class="bi bi-exclamation-triangle fs-1"></i>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Vendor List -->
  {% if vendor_data %}
    <div class="card">
      <div class="card-header">
        <h5><i class="bi bi-list me-2"></i>All Vendors</h5>
      </div>
      <div class="card-body">
        <div class="table-responsive">
          <table class="table table-striped table-hover">
            <thead class="table-dark">
              <tr>
                <th>Vendor Name</th>
                <th>Type</th>
                <th>Level</th>
                <th>Region</th>
                <th>Status</th>
                <th>Current Project</th>
                <th>Completion Rate</th>
                <th>Actions</th>
              </tr>
            </thead>
            <tbody>
              {% for data in vendor_data %}
                <tr>
                  <td>
                    <strong>{{ data.vendor.name }}</strong><br>
                    <small class="text-muted">ID: {{ data.vendor.identification_number }}</small>
                  </td>
                  <td>{{ data.vendor.get_type_display|default:"N/A" }}</td>
                  <td>
                    <span class="badge bg-secondary">Level {{ data.vendor.level|default:"N/A" }}</span>
                  </td>
                  <td>{{ data.vendor.vendor_registration.region.name|default:"N/A" }}</td>
                  <td>
                    {% if data.vendor.status == 'active' %}
                      <span class="badge bg-success">Active</span>
                    {% else %}
                      <span class="badge bg-info">Available</span>
                    {% endif %}
                  </td>
                  <td>
                    {% if data.status_info.current_project %}
                      <small>{{ data.status_info.project_name|truncatechars:30 }}</small>
                    {% else %}
                      <span class="text-muted">No active project</span>
                    {% endif %}
                  </td>
                  <td>
                    <div class="progress" style="height: 20px;">
                      <div class="progress-bar bg-success" 
                           style="width: {{ data.classification.completion_rate }}%;" 
                           role="progressbar">
                        {{ data.classification.completion_rate }}%
                      </div>
                    </div>
                  </td>
                  <td>
                    <div class="btn-group" role="group">
                      <a href="{% url 'vendor_management_detail' vendor_id=data.vendor.id %}" 
                         class="btn btn-sm btn-info">
                        <i class="bi bi-eye"></i>
                      </a>
                      <a href="{% url 'vendor_detail' vendor_id=data.vendor.vendor_registration.id %}" 
                         class="btn btn-sm btn-secondary">
                        <i class="bi bi-person-vcard"></i>
                      </a>
                    </div>
                  </td>
                </tr>
              {% endfor %}
            </tbody>
          </table>
        </div>
      </div>
    </div>
  {% else %}
    <div class="alert alert-info">
      <i class="bi bi-info-circle me-2"></i>
      No vendors found. <a href="{% url 'vendors_registration' %}">Register the first vendor</a>.
    </div>
  {% endif %}
</div>
{% endblock %}
