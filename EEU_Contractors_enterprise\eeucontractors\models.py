from django.db import models

# Create your models here.
from django.db import models
from django.db.models import Sum
import logging
logger = logging.getLogger(__name__)
from django.conf import settings  # ✅ correct import
from django.contrib.auth.models import User
from django.utils import timezone
from django.core.exceptions import ValidationError
import uuid
from django.core.validators import MinValueValidator, MaxValueValidator
from django.contrib.auth.models import AbstractUser, BaseUserManager


# 1. Custom User Manager
class CustomUserManager(BaseUserManager):
    def create_user(self, username, password=None, region=None, **extra_fields):
        if not username:
            raise ValueError("Username is required.")
        if not password:
            raise ValueError("Password is required.")
        if not region and not extra_fields.get("is_superuser", False):
            raise ValueError("Normal users must have a region.")

        extra_fields.setdefault("is_active", True)
        user = self.model(username=username, region=region, **extra_fields)
        user.set_password(password)
        user.save(using=self._db)
        return user

    def create_superuser(self, username, password=None, region=None, **extra_fields):
        extra_fields.setdefault("is_staff", True)
        extra_fields.setdefault("is_superuser", True)
        extra_fields.setdefault("is_active", True)
        return self.create_user(username, password, region, **extra_fields)


def generate_custom_id(contractor_type):
    base_uuid = uuid.uuid4().hex[:8].upper()
    if contractor_type == "contractor":
        return f"ctr-{base_uuid}"
    elif contractor_type == "enterprise":
        return f"etr-{base_uuid}"
    elif contractor_type == "new_enterprise":
        return f"netr-{base_uuid}"
    else:
        return f"UNK-{base_uuid}"
class Vendorsregistration(models.Model):
    CONTRACTOR_TYPE_CHOICES = [
        ("contractor", "Contractor"),
        ("enterprise", "Enterprise"),
        ("new_enterprise", "New Enterprise"),
    ]
    ELECTRIC_LEVEL_CHOICES = [
        ('1', 'Level 1'),
        ('2', 'Level 2'),
        ('3', 'Level 3'),
        ('4', 'Level 4'),
        ('5', 'Level 5'),
        ('6', 'Level 6'),
        ('7', 'Level 7'),
        ('8', 'Level 8'),
    ]
    contractor_type = models.CharField(max_length=20, choices=CONTRACTOR_TYPE_CHOICES, verbose_name="የኮንትራክተሩ አይነት")
    contractor_name = models.CharField(max_length=255, verbose_name="የተመዘገበው ስም")
    region = models.ForeignKey("Region", on_delete=models.CASCADE, verbose_name="ሪጅን", related_name="vendors")
    phone_number = models.CharField(max_length=20, verbose_name="ስልክ ቁጥር")
    experience = models.PositiveIntegerField(verbose_name="የስራ ልምድ (በዓመት)", help_text="Years of experience")
    email = models.EmailField(verbose_name="ኢሜይል አድራሻ")
    identification_number = models.CharField(
        max_length=100,
        unique=True,
        blank=True,
        verbose_name="የመወዳደሪያ መለያ ቁጥር"
    )
    has_professional_license = models.BooleanField(verbose_name="የሙያ ብቃት አቅርቧል?", default=False)
    electric_grade = models.CharField(max_length=2, choices=ELECTRIC_LEVEL_CHOICES, blank=True, verbose_name="የኤሌክትሪክ ስራ ተቋራጭ ደረጃ")
    has_human_resource = models.BooleanField(verbose_name="የሰው ኃይል አሟልቷል?", default=False)
    electrical_engineers = models.PositiveIntegerField(verbose_name="የኤሌክትሪካል ምህንድስና", validators=[MinValueValidator(1)])
    electromechanical_engineers = models.PositiveIntegerField(verbose_name="የኤሌክትሮ መካኒካል ምህንድስና", validators=[MinValueValidator(1)])
    civil_engineers = models.PositiveIntegerField(verbose_name="የሲቪል ምህንድስና", validators=[MinValueValidator(1)])
    electricians_foremen = models.PositiveIntegerField(verbose_name="ኤሌክትሪሺያን/ፎርማን", validators=[MinValueValidator(1)])
    surveyors = models.PositiveIntegerField(verbose_name="ሰርቬየር", validators=[MinValueValidator(1)])
    line_workers = models.PositiveIntegerField(verbose_name="የመስመር ሰራተኞች", validators=[MinValueValidator(1)])
    has_work_experience_doc = models.BooleanField(verbose_name="የሥራ ልምድ ማስረጃ አቅርቧል?", default=False)
    has_trade_license = models.BooleanField(verbose_name="የንግድ ፈቃድ አለው?", default=False)
    has_energy_certification = models.BooleanField(verbose_name="የኤሌክትሪክ ደረጃ የምስክር ወረቀት አቅርቧል?", default=False)
    has_equipment_resources = models.BooleanField(verbose_name="የሰው ኃይልና መሳሪያ አሟልቷል?", default=False)
    has_vat_and_tax = models.BooleanField(verbose_name="VAT እና የግብር የምስክር ወረቀት አቅርቧል?", default=False)
    has_good_performance = models.BooleanField(verbose_name="የመልካም ስራ አፈጻጸም አቅርቧል?", default=False)
    has_financial_capacity = models.BooleanField(verbose_name="የፋይናንስ አቅም መረጃ አቅርቧል?", default=False)
    registrar_name = models.CharField(max_length=255, verbose_name="የመዝጋቢ ሰራተኛው ስም")
    registrar_id = models.CharField(max_length=50, verbose_name="መለያ ቁጥር")
    registration_date = models.DateTimeField(
        verbose_name="የተመዘገበበት ቀን", 
        null=False,  # Change to False to disallow null values
        blank=False,  # Change to False to make it required
        default=timezone.now  # Add default value to handle existing records
    )
    def clean(self):
        """Validate contractor_type and electric_grade relationship"""
        if self.contractor_type and self.electric_grade:
            grade = int(self.electric_grade)

            # Validate contractor type-level relationship
            if self.contractor_type == 'contractor' and grade not in [1, 2, 3]:
                raise ValidationError({
                    'electric_grade': f'Contractor type must have electric grade 1, 2, or 3. Selected: {grade}'
                })
            elif self.contractor_type == 'enterprise' and grade not in [4, 5, 6]:
                raise ValidationError({
                    'electric_grade': f'Enterprise type must have electric grade 4, 5, or 6. Selected: {grade}'
                })
            elif self.contractor_type == 'new_enterprise' and grade not in [7, 8]:
                raise ValidationError({
                    'electric_grade': f'New Enterprise type must have electric grade 7 or 8. Selected: {grade}'
                })

        # Validate all staff counts are >= 1
        staff_fields = [
            ('electrical_engineers', self.electrical_engineers),
            ('electromechanical_engineers', self.electromechanical_engineers),
            ('civil_engineers', self.civil_engineers),
            ('electricians_foremen', self.electricians_foremen),
            ('surveyors', self.surveyors),
            ('line_workers', self.line_workers),
        ]

        for field_name, value in staff_fields:
            if value < 1:
                raise ValidationError({
                    field_name: f'This field must be at least 1. Current value: {value}'
                })

    def save(self, *args, **kwargs):
            if not self.identification_number:
                self.identification_number = generate_custom_id(self.contractor_type)

            if not self.registration_date:
                self.registration_date = timezone.now()
            super().save(*args, **kwargs)
    @property
    def created_at(self):
        """
        Compatibility method to maintain backward compatibility with templates
        that might still use created_at
        """
        return self.registration_date

    def __str__(self):
        return self.contractor_name
class RegionChoices(models.TextChoices):
    EAST_AA = "EAST_AA", "EAST AA"
    SOUTH_AA = "SOUTH_AA", "SOUTH AA"
    WEST_AA = "WEST_AA", "WEST AA "
    NORTH_AA = "NORTH_AA", "NORTH AA"
    AFAR = "AFAR", "AFAR" 
    BAHIRDAR = "BAHIRDAR", "BAHIRDAR"
    DESSIE = "DESSIE", "DESSIE"
    GONDER = "GONDER", "GONDER"
    DEBRE_BIRHAN = "DEBRE_BIRHAN", "DEBRE BIRHAN"
    DEBRE_MARKOS = "DEBRE_MARKOS", "DEBRE MARKOS "
    WOLEDIYA = "WOLEDIYA", "WOLEDIYA"
    BENISHANGULE_GUMUZ = "BENISH" 
    DIRE_DAWA = "DIRE_DAWA", "DIRE DAWA "
    GAMBELA = "GAMBELA", "GAMBELA"
    HARERE = "HARERE", "HARERE"
    ADAMA = "ADAMA", "ADAMA"
    SHEGER = "SHEGER", "Sheger "
    SHASHEMENE = "SHASHEMENE", "SHASHEMENE"
    CHIRO = "CHIRO", "CHIRO"
    JIMMA = "JIMMA", "JIMMA"
    NEKEMITE = "NEKEMITE", "NEKEMITE"
    AMBO = "AMBO", "AMBO"
    BALE_ROBE = "BALE_ROBE", "BALE ROBE"
    METU = "METU", "METU"
    SOMALI = "SOMALI", "SOMALI"
    ARBAMINICH = "ARBAMINICH", "ARBAMINICH "
    CENTRAL_ETHIOPIA = "CENTRAL_ETHIOPIA", "Central Ethiopia"
    WELAYITA = "WELAYITA", "WELAYITA" 
    MEKELE = "MEKELE", "MEKELE"
    SHIRE = "SHIRE", "SHIRE "
    SIDAMA = "SIDAMA", "SIDAMA"
    SOUTH_WEST = "SOUTH_WEST", "SOUTH WEST"
    South_Ethiopia = "SOUTH_ETHIOPIA", "SOUTH ETHIOPIA"
class RegioncodeChoice(models.TextChoices):
    AA = 'AA', 'AA'
    AB = 'AB', 'AB'
    AC = 'AC', 'AC'
    AD = 'AD', 'AD'
    BA = 'BA', 'BA'
    BB = 'BB', 'BB'
    BC = 'BC', 'BC'
    BD = 'BD', 'BD'
    BE = 'BE', 'BE'
    BF = 'BF', 'BF'
    BG = 'BG', 'BG'
    BH = 'BH', 'BH'
    BI = 'BI', 'BI'
    CA = 'CA', 'CA'
    CB = 'CB', 'CB'
    CC = 'CC', 'CC'
    CD = 'CD', 'CD'
    CE = 'CE', 'CE'
    CF = 'CF', 'CF'
    DB = 'DB', 'DB'
    DC = 'DC', 'DC'
    DE = 'DE', 'DE'
    EA = 'EA', 'EA'
    EB = 'EB', 'EB'
    FA = 'FA', 'FA'
    GA = 'GA', 'GA'
    HA = 'HA', 'HA'
    IA = 'IA', 'IA'
    JA = 'JA', 'JA'
    KA = 'KA', 'KA'
    LA = 'LA', 'LA'
    MA = 'MA', 'MA'
class Region(models.Model):
    name = models.CharField(
        max_length=100,
        choices=RegionChoices.choices,
        unique=True
    )
    region_code = models.CharField(
        max_length=10,
        choices=RegioncodeChoice.choices
    )

    def __str__(self):
        return f"{self.name} ({self.region_code})"

    @property
    def active_projects(self):
        """Get active projects in this region"""
        return self.projects.filter(status='active').count()

    @property
    def total_vendors(self):
        """Get total vendors registered in this region"""
        return self.vendors.count()

    class Meta:
        ordering = ['region_code', 'name']
# 3. EEU Organizational Structure Models
class EEUHeadOffice(models.Model):
    name = models.CharField(max_length=100, default="EEU Head Office")

    def __str__(self):
        return self.name

    class Meta:
        verbose_name = "EEU Head Office"
        verbose_name_plural = "EEU Head Office"

# 4. Enhanced Region Model (update existing)
# Note: Your existing Region model should be enhanced to include organizational hierarchy

# 4. Custom User Model with organizational access
class CustomUser(AbstractUser):
    region = models.ForeignKey(Region, null=True, blank=True, on_delete=models.SET_NULL)

    # User role within the organization
    ROLE_CHOICES = [
        ('ho_admin', 'Head Office Administrator'),
        ('ho_manager', 'Head Office Manager'),
        ('regional_admin', 'Regional Administrator'),
        ('regional_manager', 'Regional Manager'),
        ('project_coordinator', 'Project Coordinator'),
        ('data_entry', 'Data Entry Operator'),
    ]
    role = models.CharField(max_length=20, choices=ROLE_CHOICES, default='data_entry')

    objects = CustomUserManager()

    def clean(self):
        # Role-based validation
        if self.role in ['ho_admin', 'ho_manager'] and self.region:
            raise ValidationError("Head Office users should not be assigned to regions.")

        if self.role in ['regional_admin', 'regional_manager'] and not self.region:
            raise ValidationError("Regional users must be assigned to a region.")

    @property
    def organizational_level(self):
        """Return the organizational level of the user"""
        if self.role in ['ho_admin', 'ho_manager']:
            return 'head_office'
        elif self.role in ['regional_admin', 'regional_manager']:
            return 'regional'
        else:
            return 'operational'

    def get_accessible_regions(self):
        """Get regions this user can access based on their role"""
        if self.organizational_level == 'head_office':
            return Region.objects.all()
        elif self.region:
            return Region.objects.filter(id=self.region.id)
        else:
            return Region.objects.none()

    def get_accessible_projects(self):
        """Get projects this user can access"""
        accessible_regions = self.get_accessible_regions()
        return Project.objects.filter(region__in=accessible_regions)


# 6. Regional Performance Tracking
class RegionalPerformance(models.Model):
    """Track performance metrics for each region"""
    region = models.OneToOneField(Region, on_delete=models.CASCADE, related_name='performance')

    # Project Statistics
    total_projects = models.PositiveIntegerField(default=0)
    completed_projects = models.PositiveIntegerField(default=0)
    active_projects = models.PositiveIntegerField(default=0)
    delayed_projects = models.PositiveIntegerField(default=0)

    # Vendor Statistics
    total_vendors = models.PositiveIntegerField(default=0)
    active_vendors = models.PositiveIntegerField(default=0)
    blacklisted_vendors = models.PositiveIntegerField(default=0)

    # Performance Metrics
    average_project_completion_time = models.FloatField(default=0.0, help_text="Average days to complete projects")
    average_progress_percentage = models.FloatField(default=0.0, help_text="Average progress across all active projects")

    # Financial Metrics (if applicable)
    total_project_value = models.DecimalField(max_digits=15, decimal_places=2, default=0, help_text="Total value of all projects")
    completed_project_value = models.DecimalField(max_digits=15, decimal_places=2, default=0, help_text="Value of completed projects")

    last_updated = models.DateTimeField(auto_now=True)

    def update_statistics(self):
        """Update all statistics for this region"""
        from django.db.models import Avg, Sum, Count

        # Project statistics
        projects = Project.objects.filter(region=self.region)
        self.total_projects = projects.count()
        self.completed_projects = projects.filter(status='completed').count()
        self.active_projects = projects.filter(status='active').count()
        self.delayed_projects = projects.filter(status='suspended').count()

        # Vendor statistics
        vendors = Vendor.objects.filter(vendor_registration__region=self.region)
        self.total_vendors = vendors.count()
        self.active_vendors = vendors.filter(status='active').count()

        # Performance metrics
        active_projects = projects.filter(status='active')
        if active_projects.exists():
            avg_progress = active_projects.aggregate(
                avg_progress=Avg('progress__progress_percentage')
            )['avg_progress'] or 0
            self.average_progress_percentage = round(avg_progress, 2)

        self.save()

    @property
    def completion_rate(self):
        """Calculate project completion rate"""
        if self.total_projects == 0:
            return 0
        return round((self.completed_projects / self.total_projects) * 100, 2)

    @property
    def vendor_utilization_rate(self):
        """Calculate vendor utilization rate"""
        if self.total_vendors == 0:
            return 0
        return round((self.active_vendors / self.total_vendors) * 100, 2)

    def __str__(self):
        return f"Performance - {self.region.name}"

    class Meta:
        verbose_name = "Regional Performance"
        verbose_name_plural = "Regional Performance"


# 6. Main Activity (Define the 5 standardized electrical work types)
class MainActivity(models.Model):
    class TitleChoices(models.TextChoices):
        SUBSTATION_WORKS = 'substation', '66 and 45 kV substation electrical and civil works'
        LINE_WORKS = 'lines', 'Underground and Overhead 66, 45, 33, 19 and 15 kV lines construction'
        TRANSFORMER_INSTALLATION = 'transformers', 'Installation of 33,19 and 15KV transformers and switchgear'
        LOW_VOLTAGE_LINES = 'low_voltage', '0.4/0.23 kV line construction and maintenance'
        METER_INSTALLATION = 'meters', 'Installation and connection of single and three-phase meters'

    class LevelChoices(models.TextChoices):
        LEVEL_1_2_3 = 'level_1_2_3', 'Level 1, 2 or 3'
        LEVEL_4_5_6 = 'level_4_5_6', 'Level 4, 5 or 6'
        LEVEL_7_8 = 'level_7_8', 'Level 7 or 8'

    main_activity = models.CharField(max_length=50, choices=TitleChoices.choices)
    levels = models.CharField(max_length=20, choices=LevelChoices.choices)

    def __str__(self):
        return f"{self.main_activity}"

    def get_required_levels(self):
        mapping = {
            self.TitleChoices.SUBSTATION_WORKS: [self.LevelChoices.LEVEL_1_2_3],
            self.TitleChoices.LINE_WORKS: [
                self.LevelChoices.LEVEL_1_2_3,
                self.LevelChoices.LEVEL_4_5_6,
                self.LevelChoices.LEVEL_7_8
            ],
            self.TitleChoices.TRANSFORMER_INSTALLATION: [self.LevelChoices.LEVEL_4_5_6],
            self.TitleChoices.LOW_VOLTAGE_LINES: [self.LevelChoices.LEVEL_4_5_6, self.LevelChoices.LEVEL_7_8],
            self.TitleChoices.METER_INSTALLATION: [self.LevelChoices.LEVEL_7_8],
        }
        return mapping.get(self.main_activity, [])

# 7. Regional Project Template (Ensures each region has exactly 5 standardized projects)
class RegionalProjectTemplate(models.Model):
    """
    Template to ensure each region has exactly the same 5 main activities.
    This is created automatically for each region.
    """
    region = models.ForeignKey(Region, on_delete=models.CASCADE, related_name='project_templates')
    main_activity = models.ForeignKey(MainActivity, on_delete=models.CASCADE)

    # Project template details
    is_available = models.BooleanField(default=True, help_text="Is this project slot available for assignment?")
    assigned_vendor = models.ForeignKey('Vendor', on_delete=models.SET_NULL, null=True, blank=True, help_text="Vendor assigned to this project")
    project = models.OneToOneField('Project', on_delete=models.SET_NULL, null=True, blank=True, help_text="Actual project if assigned")

    # Regional director approval
    approved_by_director = models.BooleanField(default=False)
    director_approval_date = models.DateTimeField(null=True, blank=True)
    director_notes = models.TextField(blank=True, help_text="Director's notes about this project assignment")

    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        unique_together = ['region', 'main_activity']
        verbose_name = "Regional Project Template"
        verbose_name_plural = "Regional Project Templates"
        ordering = ['region__name', 'main_activity__main_activity']

    def clean(self):
        """Validate business rules"""
        if self.assigned_vendor and not self.assigned_vendor.can_take_new_project():
            vendor_status = self.assigned_vendor.current_project_status
            raise ValidationError({
                'assigned_vendor': f"This vendor cannot take a new project. {vendor_status['message']}"
            })

        if self.assigned_vendor and self.project:
            if self.project.vendor != self.assigned_vendor:
                raise ValidationError("Assigned vendor must match the project vendor.")

    @property
    def status(self):
        """Get current status of this project template"""
        if self.project:
            return self.project.status
        elif self.assigned_vendor:
            return 'assigned_pending'
        else:
            return 'available'

    @property
    def can_assign_vendor(self):
        """Check if a vendor can be assigned to this template"""
        return not self.assigned_vendor and self.is_available

    def assign_vendor(self, vendor, director_user=None):
        """Assign a vendor to this project template"""
        if not vendor.can_take_new_project():
            raise ValidationError(f"Vendor {vendor.name} cannot take a new project.")

        if not self.can_assign_vendor:
            raise ValidationError("This project template is not available for assignment.")

        # Validate vendor type-level compliance
        is_valid, message = vendor.validate_type_level_compliance()
        if not is_valid:
            raise ValidationError(f"Vendor type-level validation failed: {message}")

        self.assigned_vendor = vendor
        self.is_available = False

        if director_user:
            self.approved_by_director = True
            self.director_approval_date = timezone.now()
            self.director_notes = f"Approved by {director_user.get_full_name() or director_user.username}"

        self.save()

    def __str__(self):
        status_display = f" - {self.assigned_vendor.name}" if self.assigned_vendor else " - Available"
        return f"{self.region.name}: {self.main_activity.main_activity}{status_display}"
from django.db import models
from django.core.exceptions import ValidationError
from django.apps import apps
from django.db.models.signals import post_save
from django.dispatch import receiver

class Vendor(models.Model):
    vendor_registration = models.ForeignKey('Vendorsregistration', on_delete=models.CASCADE)

    # All fields auto-populated from vendorsregistration
    name = models.CharField(max_length=255, editable=False, null=True)
    level = models.CharField(max_length=100, editable=False)
    type = models.CharField(max_length=100, editable=False)
    identification_number = models.CharField(max_length=100, editable=False)
    phone_number = models.CharField(max_length=20, editable=False)
    experience = models.PositiveIntegerField(editable=False)
    email = models.EmailField(editable=False)

    STATUS_CHOICES = (
        ('active', 'Active'),
        ('inactive','Inactive'),
        ('blacklisted','Blacklisted'),
    )
    status = models.CharField(max_length=15, choices=STATUS_CHOICES, default='inactive', editable=False)
    def __str__(self):
        return self.name or f"Vendor #{self.pk}"  # ✅ Fix added here
    def clean(self):
        if not self.vendor_registration or not self.vendor_registration.pk:
            raise ValidationError("Vendor registration must be selected and saved first.")
    def update_status(self):
        Project = apps.get_model('eeucontractors', 'Project')
        self.status = 'active' if Project.objects.filter(vendor=self, status='active').exists() else 'inactive'

    def get_current_project(self):
        Project = apps.get_model('eeucontractors', 'Project')
        return Project.objects.filter(
            vendor=self,
            status__in=['active', 'suspended']
        ).first()

    def can_take_new_project(self):
        Project = apps.get_model('eeucontractors', 'Project')
        has_active_project = Project.objects.filter(
            vendor=self,
            status__in=['active', 'suspended']
        ).exists()
        return not has_active_project

    def get_current_active_project(self):
        """Get the current active/suspended project for this vendor"""
        Project = apps.get_model('eeucontractors', 'Project')
        return Project.objects.filter(
            vendor=self,
            status__in=['active', 'suspended']
        ).first()

    def get_all_projects(self):
        Project = apps.get_model('eeucontractors', 'Project')
        return Project.objects.filter(vendor=self).order_by('-start_date')

    def get_completed_projects(self):
        Project = apps.get_model('eeucontractors', 'Project')
        return Project.objects.filter(vendor=self, status='completed').order_by('-end_date')

    @property
    def is_available_for_new_project(self):
        """Check if vendor is available to take a new project (not currently working)"""
        return self.can_take_new_project()

    @property
    def current_project_status(self):
        """Get current project status for this vendor"""
        current_project = self.get_current_active_project()
        if not current_project:
            return {
                'status': 'available',
                'message': 'Vendor is available to take a new project',
                'can_take_project': True,
                'current_project': None
            }
        else:
            return {
                'status': current_project.status,
                'project_name': current_project.name,
                'region': current_project.region.name,
                'main_activity': current_project.main_activity.main_activity,
                'message': f'Vendor is currently working on: {current_project.name}',
                'can_take_project': False,
                'current_project': current_project
            }

    @property
    def total_completed_projects(self):
        """Get total number of completed projects across all regions"""
        Project = apps.get_model('eeucontractors', 'Project')
        return Project.objects.filter(vendor=self, status='completed').count()

    @property
    def completion_rate(self):
        """Calculate completion rate (completed projects vs total assigned projects)"""
        all_projects = self.get_all_projects()
        total_count = all_projects.count()
        if total_count == 0:
            return 0
        completed_count = all_projects.filter(status='completed').count()
        return round((completed_count / total_count) * 100, 2)

    @property
    def total_completed_projects(self):
        """Get total number of completed projects"""
        return self.get_completed_projects().count()

    @property
    def total_projects_worked(self):
        return self.get_all_projects().count()

    def validate_type_level_compliance(self):
        """Validate that vendor type matches their level"""
        try:
            level = int(self.level) if self.level else 0
        except ValueError:
            return False, f"Invalid level: {self.level}"

        if self.type == 'contractor' and level in [1, 2, 3]:
            return True, "Contractor with valid level"
        elif self.type == 'enterprise' and level in [4, 5, 6]:
            return True, "Enterprise with valid level"
        elif self.type == 'new_enterprise' and level in [7, 8]:
            return True, "New Enterprise with valid level"
        else:
            return False, f"{self.type} type must have appropriate level (Contractor: 1-3, Enterprise: 4-6, New Enterprise: 7-8)"

    @property
    def vendor_classification(self):
        """Get vendor classification summary"""
        is_valid, message = self.validate_type_level_compliance()
        current_status = self.current_project_status

        return {
            'type': self.type,
            'level': self.level,
            'is_type_level_valid': is_valid,
            'type_level_message': message,
            'current_status': current_status['status'],
            'can_take_project': current_status['can_take_project'],
            'total_projects': self.total_projects_worked,
            'completed_projects': self.total_completed_projects,
            'completion_rate': self.completion_rate,
            'is_currently_working': not current_status['can_take_project']
        }
    def save(self, *args, **kwargs):
        if not self.vendor_registration or not self.vendor_registration.pk:
            raise ValidationError("Cannot save vendor: vendor registration must be saved first.")

        # Auto-populate all fields from vendor_registration
        self.name = self.vendor_registration.contractor_name
        self.level = self.vendor_registration.electric_grade
        self.type = self.vendor_registration.contractor_type
        self.identification_number = self.vendor_registration.identification_number
        self.phone_number = self.vendor_registration.phone_number
        self.experience = self.vendor_registration.experience
        self.email = self.vendor_registration.email

        super().save(*args, **kwargs)  # Save before status update
        self.update_status()
        super().save(update_fields=["status"])
class Project(models.Model):
    STATUS_CHOICES = [
        ('active', 'active'),
        ('completed','Completed'),
        ('delayed', 'Delayed'),
        ('suspended', 'Suspended'),
        ('cancelled', 'Cancelled'),
        ('completed with delay', 'Completed with delay')
    ]
    TYPE_CHOICES = [
        ('contractors', 'Contractors'),
        ('enterprise', 'Enterprise'),
        ('new_enterprise', 'New Enterprise'),
    ]
    name = models.CharField(max_length=100)
    region = models.ForeignKey('Region', on_delete=models.CASCADE, related_name='projects')
    vendor = models.ForeignKey('Vendor', on_delete=models.CASCADE, related_name='projects')
    level = models.PositiveIntegerField(
        validators=[MinValueValidator(1), MaxValueValidator(8)],
        help_text="Select vendor level between 1 and 8"
    )
    type = models.CharField(max_length=20, choices=TYPE_CHOICES)
    main_activity = models.ForeignKey('MainActivity', on_delete=models.CASCADE)
    start_date = models.DateField()
    estimated_end_date = models.DateField()
    end_date = models.DateField(null=True, blank=True)
    time_consumption = models.DurationField(blank=True, null=True)
    status = models.CharField(max_length=20, choices=STATUS_CHOICES)
    approved = models.BooleanField(default=False)
    approved_by = models.CharField(max_length=100, blank=True, null=True)
    approved_date = models.DateField(blank=True, null=True)
    # Map grouped level keys to actual level sets
    LEVEL_RANGE_MAP = {
        'level_1_2_3': {1, 2, 3},
        'level_4_5_6': {4, 5, 6},
        'level_7_8': {7, 8},
    }
    def clean(self):
            main_activity = getattr(self, 'main_activity', None)
            if not main_activity:
              raise ValidationError({'main_activity': "Main activity must be selected."})
            if not self.vendor.vendor_registration_id:
                raise ValidationError("Vendor registration must be selected and saved before saving the project.")
            # Prevent multiple vendor registrations
            Vendor = apps.get_model('eeucontractors', 'Vendor')
            if self.pk is None and Vendor.objects.filter(
                vendor_registration=self.vendor.vendor_registration
            ).exclude(pk=self.vendor.pk).exists():
                raise ValidationError("A vendor is already registered with this vendor registration.")
            # CRITICAL BUSINESS RULE: One vendor can only work on ONE project AT A TIME across ALL regions
            Project = apps.get_model('eeucontractors', 'Project')

            # Check if vendor has any ACTIVE or SUSPENDED projects (they can take new projects after completion)
            active_projects = Project.objects.filter(
                vendor__vendor_registration=self.vendor.vendor_registration,
                status__in=['active', 'suspended']  # Only block if currently working
            ).exclude(pk=self.pk)  # Exclude current project if updating

            if active_projects.exists():
                existing_project = active_projects.first()
                raise ValidationError({
                    'vendor': f"This vendor already has an {existing_project.status} project: '{existing_project.name}' "
                             f"in {existing_project.region.name} region. A vendor can only work on ONE project "
                             f"at a time across ALL regions. Please complete or cancel the existing project first."
                })

            # Validate vendor type and level alignment
            vendor_type = self.vendor.type
            vendor_level = int(self.vendor.level) if self.vendor.level else 0

            # Enforce vendor type-level restrictions
            if vendor_type == 'contractor' and vendor_level not in [1, 2, 3]:
                raise ValidationError({
                    'vendor': f"Contractor type vendors must be Level 1, 2, or 3. "
                             f"This vendor is Level {vendor_level}."
                })
            elif vendor_type == 'enterprise' and vendor_level not in [4, 5, 6]:
                raise ValidationError({
                    'vendor': f"Enterprise type vendors must be Level 4, 5, or 6. "
                             f"This vendor is Level {vendor_level}."
                })
            elif vendor_type == 'new_enterprise' and vendor_level not in [7, 8]:
                raise ValidationError({
                    'vendor': f"New Enterprise type vendors must be Level 7 or 8. "
                             f"This vendor is Level {vendor_level}."
                })
           # Validate level matches the vendor
            try:
                vendor_level = int(self.vendor.level)
            except ValueError:
                raise ValidationError({'level': f"Vendor level ({self.vendor.level}) must be a number between 1 and 8."})
            if self.main_activity:
                required_level_groups = self.main_activity.get_required_levels()
                allowed_levels = set()
                for group in required_level_groups:
                    allowed_levels.update(self.LEVEL_RANGE_MAP.get(group, set()))

                if self.level not in allowed_levels:
                    activity_name = dict(self.main_activity.TitleChoices.choices).get(
                        self.main_activity.main_activity,
                        str(self.main_activity)
                    )
                    raise ValidationError({
                        'level': (
                            f"Level '{self.level}' is not suitable for the selected activity '{activity_name}'. "
                            f"Allowed levels are: {sorted(allowed_levels)}."
                        )
                    })
    def save(self, *args, **kwargs):
        if self.vendor and self.vendor.level:
            self.level = self.vendor.level     
        if self.end_date and self.start_date:
            self.time_consumption = self.end_date - self.start_date
        super().save(*args, **kwargs)
        self.vendor.update_status()
        self.vendor.save()
    def __str__(self):
        return f"{self.name} - {self.status} - {self.vendor}"
# --------------------
# Sub Activities
# --------------------
class SubActivity(models.Model):
    main_activity = models.ForeignKey(MainActivity, on_delete=models.CASCADE, related_name='subactivities')
    name = models.CharField(max_length=200)
    order = models.PositiveIntegerField()
    weight = models.DecimalField(max_digits=5, decimal_places=2)
    base_duration = models.DecimalField(max_digits=6, decimal_places=2, help_text="Base duration per unit in days")

    def __str__(self):
        return f"{self.main_activity} > {self.name}"
    
    def clean(self):
        # Validate weight is between 0 and 100
        if self.weight < 0 or self.weight > 100:
            raise ValidationError({'weight': 'Weight must be between 0 and 100.'})
        
        # Optional: Validate total weights for a main activity don't exceed 100
        if self.main_activity:
            total_weight = SubActivity.objects.filter(
                main_activity=self.main_activity
            ).exclude(pk=self.pk).aggregate(Sum('weight'))['weight__sum'] or 0
            total_weight += self.weight
            if total_weight > 100:
                raise ValidationError('Total weight of all sub-activities exceeds 100%.')
#Use prefetch_related or select_related in views for deep lookups like:
class ProjectProgress(models.Model):
    STATUS_CHOICES = [
        ('active', 'active'),
        ('completed','Completed'),
        ('delayed', 'Delayed'),
        ('suspended', 'Suspended'),
        ('cancelled', 'Cancelled'),
        ('completed with delay', 'Completed with delay')
    ]
    project = models.ForeignKey(Project, on_delete=models.CASCADE, related_name='progress')
    vendor = models.ForeignKey(Vendor, on_delete=models.CASCADE)
    main_activity = models.ForeignKey(MainActivity, on_delete=models.CASCADE)
    progress_percentage = models.DecimalField(max_digits=5, decimal_places=2, default=0)
    last_updated = models.DateTimeField(auto_now=True)  # Add this field
    start_date = models.DateField(default=timezone.now)
    completion_date = models.DateField(blank=True, null=True)
    status = models.CharField(max_length=30, choices=STATUS_CHOICES)
    remarks = models.TextField(blank=True, null=True)
    updated_by = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.SET_NULL, null=True, blank=True)
    class Meta:
        ordering = ['-start_date']
    def total_progress(self):
        """
        Calculate total progress with time efficiency factored in.
        This provides a more accurate view of project health by considering both
        completion percentage and schedule adherence.
        
        Returns:
            float: Adjusted progress percentage (0-100)
        """
        total_weighted = 0
        total_weight = 0
        
        for workload in self.workloads.all():
            for progress_record in workload.progress_records.all():
                weight = workload.sub_activity.weight
                compliance_factor = progress_record.time_factor
                adjusted_progress = progress_record.progress * compliance_factor
                total_weighted += adjusted_progress * weight
                total_weight += weight
        
        if total_weight == 0:
            return 0
        
        return round(total_weighted / total_weight, 2)
    def calculate_progress(self):
        """
        Enhanced progress calculation considering weight, order, duration, and quantity.
        Also checks for missing sub-activities to ensure complete evaluation.
        """
        # Get all workloads for this project progress
        workloads = self.workloads.all()
        if not workloads.exists():
            self.progress_percentage = 0
            self.save(update_fields=['progress_percentage'])
            return 0

        # Use prefetch_related to reduce database queries
        workloads = workloads.select_related('sub_activity').prefetch_related(
            models.Prefetch(
                'progress_records',
                queryset=SubActivityProgress.objects.order_by('-end_date'),
                to_attr='prefetched_progress'
            )
        ).order_by('sub_activity__order')
        
        # Get all expected sub-activities for this main activity
        expected_sub_activities = SubActivity.objects.filter(
            main_activity=self.main_activity
        )
        
        # Check for missing sub-activities
        assigned_sub_ids = set(workload.sub_activity_id for workload in workloads)
        all_sub_ids = set(sub.id for sub in expected_sub_activities)
        missing_sub_ids = all_sub_ids - assigned_sub_ids
        
        # Log warning if sub-activities are missing
        if missing_sub_ids:
            missing_subs = SubActivity.objects.filter(id__in=missing_sub_ids)
            missing_names = ", ".join([sub.name for sub in missing_subs])
            logger.warning(f"Project {self.project.name} is missing workloads for sub-activities: {missing_names}")
        
        # Continue with existing calculation logic
        total_weighted_progress = 0
        total_weight = 0
        completed_activities = set()

        # FIRST: Calculate progress for EXISTING workloads
        for workload in workloads:
            # Base factors
            weight = float(getattr(workload.sub_activity, 'weight', 1) or 1)
            # Use quantity-based progress instead of manual progress
            quantity_progress = workload.quantity_progress_percentage
            # 1. Order Factor: Activities should follow sequence
            order_factor = self._calculate_order_factor(workload, completed_activities)
            # 2. Duration Factor: Time efficiency consideration
            latest_progress = workload.prefetched_progress[0] if workload.prefetched_progress else None
            if latest_progress:
                duration_factor = self._calculate_duration_factor(workload, latest_progress)
            else:
                duration_factor = 1.0  # No time penalty if no progress records yet
            # Combined progress for this sub-activity
            adjusted_progress = quantity_progress * order_factor * duration_factor

            # Weight the progress
            total_weighted_progress += adjusted_progress * weight
            total_weight += weight

            # Track completed activities for order factor calculation
            if quantity_progress >= 100:
                completed_activities.add(workload.sub_activity.order)

        # SECOND: Include MISSING workloads with 0% progress but their weights
        if missing_sub_ids:
            missing_subs = SubActivity.objects.filter(id__in=missing_sub_ids)
            for missing_sub in missing_subs:
                # Missing sub-activities contribute 0% progress but their weight counts
                missing_weight = float(getattr(missing_sub, 'weight', 1) or 1)
                # 0% progress for missing workloads
                total_weighted_progress += 0 * missing_weight  # This is 0, but explicit for clarity
                total_weight += missing_weight
        if total_weight == 0:
            self.progress_percentage = 0
        else:
            self.progress_percentage = round(total_weighted_progress / total_weight, 2)

        # Auto-set completion date when progress reaches 100%
        if self.progress_percentage >= 100 and not self.completion_date:
            self.completion_date = timezone.now().date()
            self.status = 'completed'
            self.save(update_fields=['progress_percentage', 'completion_date', 'status'])
        else:
            self.save(update_fields=['progress_percentage'])

        return self.progress_percentage

    def _calculate_order_factor(self, workload, completed_activities):
        """
        Calculate order dependency factor.
        Activities should generally follow sequence, but allow some flexibility.
        """
        current_order = workload.sub_activity.order

        # If this is the first activity, no dependency
        if current_order <= 1:
            return 1.0

        # Check if previous activities are reasonably progressed
        previous_orders = [order for order in completed_activities if order < current_order]
        expected_previous = current_order - 1

        if expected_previous in completed_activities:
            return 1.0  # Previous activity completed
        elif len(previous_orders) >= (expected_previous * 0.7):
            return 0.9  # Most previous activities done
        else:
            return 0.8  # Some dependency issues, but allow progress

    def _calculate_duration_factor(self, workload, latest_progress):
        """
        Calculate time efficiency factor based on expected vs actual duration.
        """
        if not latest_progress.start_date or not latest_progress.end_date:
            return 1.0

        actual_duration = (latest_progress.end_date - latest_progress.start_date).days + 1
        expected_duration = workload.expected_duration or workload.sub_activity.base_duration

        if actual_duration <= expected_duration:
            return 1.0  # On time or early
        else:
            # Penalize for delays, but not too harshly
            efficiency = float(expected_duration) / float(actual_duration)
            return max(efficiency, 0.7)  # Minimum 70% factor

    def _calculate_quantity_factor(self, workload):
        """
        Calculate quantity completion factor based on actual completed work.
        """
        if workload.quantity == 0:
            return 0.0
        return workload.completed_quantity / workload.quantity
    def get_progress_timeline(self):
        """
        Generate timeline data for progress visualization.
        
        Returns:
            list: Timeline data with dates and progress percentages
        """
        # Get all progress records across all workloads
        all_progress = SubActivityProgress.objects.filter(workload__project_progress=self).select_related('workload__sub_activity').order_by('end_date')
        
        # Group by date and calculate weighted progress
        timeline = {}
        date_weights = {}
        
        for progress in all_progress:
            date_str = progress.end_date.isoformat()
            weight = progress.workload.sub_activity.weight
            
            if date_str not in timeline:
                timeline[date_str] = 0
                date_weights[date_str] = 0
            
            timeline[date_str] += progress.progress * weight
            date_weights[date_str] += weight
        
        # Calculate percentage for each date
        result = []
        cumulative_progress = 0
        
        for date_str, weighted_sum in sorted(timeline.items()):
            if date_weights[date_str] > 0:
                date_progress = weighted_sum / date_weights[date_str]
                # Use the higher of current calculation or previous cumulative
                cumulative_progress = max(cumulative_progress, date_progress)
                
                result.append({
                    'date': date_str,
                    'progress': round(cumulative_progress, 2)
                })
        
        return result
    
    def get_time_performance(self):
        if not self.start_date:
            return {'status': 'not_started'}
          
        today = timezone.now().date()
        
        # Calculate planned duration based on workloads
        planned_duration = 0
        for workload in self.workloads.all():
            if workload.expected_duration:
                planned_duration = max(planned_duration, float(workload.expected_duration))
        
        # Calculate planned end date
        planned_end_date = self.start_date + timezone.timedelta(days=planned_duration) if planned_duration else None
        
        # Calculate actual duration so far
        actual_duration = (self.completion_date or today) - self.start_date
        actual_duration_days = actual_duration.days
        
        # Calculate time efficiency
        if planned_duration and actual_duration_days > 0:
            time_efficiency = min(planned_duration / actual_duration_days, 1.0) if self.progress_percentage == 100 else \
                              (self.progress_percentage / 100) / (actual_duration_days / planned_duration)
        else:
            time_efficiency = 1.0
        
        return {
            'planned_duration': planned_duration,
            'planned_end_date': planned_end_date,
            'actual_duration': actual_duration_days,
            'time_efficiency': round(time_efficiency, 2),
            'status': 'ahead' if time_efficiency > 1.0 else 'on_track' if time_efficiency >= 0.9 else 'behind'
        }
    def create_missing_workloads(self, default_quantity=1):
        """
        Create workloads for any missing sub-activities.
        Returns the number of workloads created.

        Args:
            default_quantity (int): Default quantity for new workloads (affects duration calculation)
        """
        # Get all expected sub-activities for this main activity
        expected_sub_activities = SubActivity.objects.filter(
            main_activity=self.main_activity
        )

        # Get existing workload sub-activity IDs
        existing_sub_ids = set(
            self.workloads.values_list('sub_activity_id', flat=True)
        )

        # Find missing sub-activities
        missing_subs = expected_sub_activities.exclude(id__in=existing_sub_ids)

        # Create workloads for missing sub-activities
        new_workloads = []
        for sub in missing_subs:
            # Calculate expected_duration based on quantity and base_duration
            calculated_duration = int(float(sub.base_duration) * default_quantity)

            new_workloads.append(SubActivityWorkload(
                project_progress=self,
                sub_activity=sub,
                quantity=default_quantity,  # Use provided quantity
                unit_type='units',  # Default unit type
                expected_duration=calculated_duration  # Duration = base_duration * quantity
            ))

        # Bulk create the new workloads
        if new_workloads:
            SubActivityWorkload.objects.bulk_create(new_workloads)

        return len(new_workloads)

    def get_completion_status(self):
        """
        Check if all sub-activities are completed and return detailed status.
        """
        # Get all expected sub-activities for this main activity
        expected_sub_activities = SubActivity.objects.filter(
            main_activity=self.main_activity
        )
        total_expected = expected_sub_activities.count()

        # Get workloads with their completion status
        workloads = self.workloads.select_related('sub_activity').all()
        total_assigned = workloads.count()
        completed_workloads = sum(1 for w in workloads if w.quantity_progress_percentage >= 100)

        # Calculate completion percentage
        if total_expected == 0:
            completion_percentage = 0
        else:
            completion_percentage = (completed_workloads / total_expected) * 100

        return {
            'total_expected': total_expected,
            'total_assigned': total_assigned,
            'completed_workloads': completed_workloads,
            'missing_workloads': total_expected - total_assigned,
            'completion_percentage': round(completion_percentage, 2),
            'is_fully_complete': completion_percentage >= 100,
            'has_missing_workloads': total_assigned < total_expected
        }

    def auto_create_missing_workloads(self, default_quantity=1):
        """
        Automatically create missing workloads and return status.

        Args:
            default_quantity (int): Default quantity for new workloads (affects duration calculation)
        """
        created_count = self.create_missing_workloads(default_quantity)
        if created_count > 0:
            # Recalculate progress after creating missing workloads
            self.calculate_progress()
        return created_count

    def __str__(self):
        return f"{self.project.name} - {self.vendor.name} ({self.status})"

class SubActivityWorkload(models.Model):
    project_progress = models.ForeignKey(ProjectProgress, on_delete=models.CASCADE, related_name='workloads')
    sub_activity = models.ForeignKey(SubActivity, on_delete=models.CASCADE)
    quantity = models.PositiveIntegerField(default=1, validators=[MinValueValidator(1)], help_text="Total quantity of work units")
    completed_quantity = models.PositiveIntegerField(default=0, validators=[MinValueValidator(0)], help_text="Completed quantity of work units")
    unit_type = models.CharField(max_length=50, default='units', help_text="Type of units (poles, meters, transformers, etc.)")
    expected_duration = models.PositiveIntegerField(default=1, help_text="Expected duration in days (auto-calculated: base_duration × quantity)", validators=[MinValueValidator(1)])

    class Meta:
        unique_together = ['project_progress', 'sub_activity']
        ordering = ['sub_activity__order']

    def clean(self):
        """Validate that sub_activity belongs to the same main_activity as project_progress"""
        # Only validate main_activity match if both fields are set and project_progress is saved
        if (self.sub_activity and self.project_progress_id and
            hasattr(self, 'project_progress') and self.project_progress):
            try:
                if self.sub_activity.main_activity != self.project_progress.main_activity:
                    raise ValidationError({
                        'sub_activity': f"Sub-activity must belong to the same main activity ({self.project_progress.main_activity}) as the project progress."
                    })
            except AttributeError:
                # project_progress relationship not accessible yet, skip validation
                pass

        # Validate completed_quantity doesn't exceed total quantity
        if self.completed_quantity > self.quantity:
            raise ValidationError({
                'completed_quantity': f"Completed quantity ({self.completed_quantity}) cannot exceed total quantity ({self.quantity})."
            })

    def save(self, *args, **kwargs):
        """
        Auto-calculate expected_duration based on quantity and sub_activity base_duration
        This ensures fair evaluation: larger projects (more quantity) get proportionally longer duration
        """
        if self.sub_activity and self.quantity:
            # Calculate expected_duration = base_duration × quantity
            self.expected_duration = int(float(self.sub_activity.base_duration) * self.quantity)

            # Ensure minimum duration of 1 day
            if self.expected_duration < 1:
                self.expected_duration = 1

        super().save(*args, **kwargs)

    @property
    def quantity_progress_percentage(self):
        """Calculate progress based on completed vs total quantity"""
        if self.quantity == 0:
            return 0
        return round((self.completed_quantity / self.quantity) * 100, 2)

    @property
    def current_progress(self):
        """Get the current progress percentage for this workload - uses quantity-based calculation"""
        return self.quantity_progress_percentage

    @property
    def latest_progress(self):
        """Get the latest progress record for this workload"""
        return self.progress_records.order_by('-end_date').first()

    @property
    def remaining_quantity(self):
        """Get remaining quantity to be completed"""
        return self.quantity - self.completed_quantity

    def __str__(self):
        return f"{self.sub_activity.name} ({self.completed_quantity}/{self.quantity} {self.unit_type})"
class SubActivityProgress(models.Model):
    workload = models.ForeignKey(SubActivityWorkload, on_delete=models.CASCADE, related_name='progress_records')
    progress = models.PositiveSmallIntegerField(validators=[MinValueValidator(0), MaxValueValidator(100)])
    start_date = models.DateField()
    end_date = models.DateField()

    class Meta:
        ordering = ['-end_date']
        indexes = [
            models.Index(fields=['workload', '-end_date']),
        ]

    def clean(self):
        """Validate date logic and progress constraints"""
        if self.start_date and self.end_date:
            if self.start_date > self.end_date:
                raise ValidationError({
                    'end_date': 'End date cannot be earlier than start date.'
                })

        # Validate that progress doesn't decrease (unless it's a correction)
        if self.workload_id:
            latest_progress = self.workload.progress_records.exclude(pk=self.pk).order_by('-end_date').first()
            if latest_progress and self.progress < latest_progress.progress:
                if self.end_date <= latest_progress.end_date:
                    raise ValidationError({
                        'progress': f'Progress cannot decrease from {latest_progress.progress}% to {self.progress}% unless this is a correction for an earlier date.'
                    })

    @property
    def actual_duration(self):
        """Calculate the actual duration in days between start and end date"""
        if self.start_date and self.end_date:
            return (self.end_date - self.start_date).days + 1
        return 0
    @property
    def time_factor(self):
        """
        Calculate time efficiency factor (1.0 = on time, <1.0 = behind schedule)
        This helps track if work is being completed according to schedule
        """
        if not self.workload.expected_duration or self.actual_duration == 0:
            return 1.0

        expected = float(self.workload.expected_duration)
        actual = float(self.actual_duration)

        # If completed early or on time
        if actual <= expected:
            return 1.0

        # If late, calculate efficiency factor (minimum 0.7)
        factor = expected / actual
        return max(factor, 0.7)

    def __str__(self):
        return f"{self.workload.sub_activity.name}: {self.progress}%"
class ProjectVendorsHistory(models.Model):
    STATUS_CHOICES = [
        ('completed', 'Completed'),
        ('delayed', 'Delayed'),
        ('suspended', 'Suspended'),
        ('blacklisted', 'Blacklisted'),
    ]
    DELAY_REASON_CHOICES = [
        ('vendor', 'Vendor'),
        ('organization', 'Organization'),
        ('both', 'Both'),
        ('unknown', 'Unknown'),
    ]
    vendor = models.ForeignKey('Vendor', on_delete=models.CASCADE)
    project = models.ForeignKey('Project', on_delete=models.SET_NULL, null=True) 
    status = models.CharField(
        max_length=20,
        choices=STATUS_CHOICES,
        default='completed'
    )
    evaluation_notes = models.TextField(blank=True, null=True)
    start_date = models.DateField()
    end_date = models.DateField()
    cause_of_delay_or_suspension = models.CharField(
        max_length=20,
        choices=DELAY_REASON_CHOICES,
        blank=True,
        null=True,
        help_text="Specify who caused the delay or suspension if applicable"
    )
    def clean(self):
        if self.status in ['delayed', 'suspended'] and not self.cause_of_delay_or_suspension:
            raise ValidationError("You must specify the cause of delay or suspension.")
        if self.status == 'completed':
            self.cause_of_delay_or_suspension = None
    def __str__(self):
        return f"{self.project} - {self.status}"

class VendorDocument(models.Model):
    DOCUMENT_TYPES = (
        ('license', 'License'),
        ('certificate', 'Certificate'),
        ('id_proof', 'ID Proof'),
        ('other', 'Other'),
    )
    vendor = models.ForeignKey(Vendor, on_delete=models.CASCADE, related_name='documents')
    document_type = models.CharField(max_length=50, choices=DOCUMENT_TYPES)
    file = models.FileField(upload_to='vendor_documents/')
    description = models.TextField(max_length=100,blank=True)
    expiry_date=models.DateField()
    uploaded_at = models.DateTimeField(auto_now_add=True)
    def __str__(self):
          return f"{self.vendor.name} - {self.get_document_type_display()}"


# ============================================================================
# SIGNALS - Auto-create Vendor when Vendorsregistration is created
# ============================================================================

@receiver(post_save, sender=Vendorsregistration)
def create_vendor_from_registration(sender, instance, created, **kwargs):
    """
    Automatically create a Vendor instance when a Vendorsregistration is created.
    This ensures every registered vendor has a corresponding Vendor record for project management.
    """
    if created:
        try:
            # Check if Vendor already exists (safety check)
            if not Vendor.objects.filter(vendor_registration=instance).exists():
                vendor = Vendor.objects.create(vendor_registration=instance)
                print(f"✅ Auto-created Vendor for: {instance.contractor_name}")
        except Exception as e:
            print(f"❌ Error creating Vendor for {instance.contractor_name}: {e}")


@receiver(post_save, sender=Vendorsregistration)
def update_vendor_from_registration(sender, instance, created, **kwargs):
    """
    Update existing Vendor when Vendorsregistration is updated.
    This keeps the Vendor fields in sync with the registration.
    """
    if not created:  # Only for updates, not new creations
        try:
            vendor = Vendor.objects.get(vendor_registration=instance)
            # The save() method will auto-populate fields from vendor_registration
            vendor.save()
            print(f"✅ Updated Vendor for: {instance.contractor_name}")
        except Vendor.DoesNotExist:
            # If somehow the Vendor doesn't exist, create it
            vendor = Vendor.objects.create(vendor_registration=instance)
            print(f"✅ Created missing Vendor for: {instance.contractor_name}")
        except Exception as e:
            print(f"❌ Error updating Vendor for {instance.contractor_name}: {e}")

