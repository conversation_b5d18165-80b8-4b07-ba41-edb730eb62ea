from django.urls import path
from django.contrib.auth.views import LogoutView
from . import views
from django.contrib.auth import views as auth_views
from django.contrib.auth.views import LoginView

urlpatterns = [

 # Keep all existing URL patterns
    path('', views.login_view, name='login'),
    path('home/', views.home, name='home'),
    path('logout/', views.logout_view, name='logout'),
    path('dashboard/', views.dashboard, name='dashboard'),
    # Vendor Registration
    path('vendors/register/', views.vendors_registration_view, name='vendors_registration'),
 
    path('vendors/list/', views.vendors_list_view, name='vendors_list'),
   #  path('vendors/registered/', views.registered_contractors_list, name='registered_contractors_list'),
   #  path('vendors/enterprises/', views.new_enterprises_list, name='new_enterprises_list'),

    path('vendors/<int:vendor_id>/', views.vendor_detail_view, name='vendor_detail'),
    # path('create/', views.vendor_create_view, name='vendor_create'),
    path('list/', views.vendor_list_view, name='vendor_information_list'),

    # Projects - use the existing function-based view
    path('projects/', views.Project_List.as_view(), name='project_list'), 
    path('projects/create/', views.project_create, name='project_create'),
    path('projects/<int:pk>/edit/', views.project_edit, name='project_edit'),
    
    # Project Progress
    path('progress/', views.project_progress_list, name='project_progress_list'),
    path('progress/create/', views.create_project_progress, name='create_project_progress'),
    path('progress/<int:pk>/edit/', views.project_progress_update, name='project_progress_update'),
    path('progress/<int:pk>/delete/', views.project_progress_delete, name='project_progress_delete'),
    path('progress/<int:pk>/detail/', views.project_progress_detail, name='project_progress_detail'),
    
    # Workload Management URLs
    path('workloads/', views.all_workloads_overview, name='all_workloads'),
    path('workloads/detail/<int:workload_id>/', views.workload_detail, name='workload_detail'),
    path('workloads/edit/<int:workload_id>/', views.edit_workload, name='edit_workload'),
    path('workloads/delete/<int:workload_id>/', views.delete_workload, name='delete_workload'),
    path('workloads/<int:progress_id>/add/', views.add_workload, name='add_workload'),
    path('workloads/<int:progress_id>/', views.SubActivityWorkloadListView.as_view(), name='workload_list'),
    path('workloads/<int:progress_id>/debug/', views.debug_workload_creation, name='debug_workload_creation'),

    # Progress Selection
    path('progress/select/<int:progress_id>/', views.select_project_progress, name='select_project_progress'),

    # Vendor History
    path('vendor-history/', views.VendorHistoryListView.as_view(), name='vendor_history_list'),
    path('vendor/<int:vendor_id>/history/', views.VendorHistoryListView.as_view(), name='vendor_history_list'),
    path('vendor-history/add/', views.add_vendor_history, name='add_vendor_history'),
    path('vendor/<int:vendor_id>/history/add/', views.add_vendor_history, name='add_vendor_history'), 
    path('project/<int:project_id>/history/add/', views.add_vendor_history, name='add_project_history'), 
    path('vendor-history/<int:history_id>/', views.vendor_history_detail, name='vendor_history_detail'),
    path('vendor-history/<int:history_id>/edit/', views.edit_vendor_history, name='edit_vendor_history'),
    
       #Documents 
    path('vendor-documents/', views.VendorDocumentListView.as_view(), name='all_vendor_documents'),
    path('vendor/<int:vendor_id>/documents/', views.VendorDocumentListView.as_view(), name='vendor_document_list'),
    path('documents/add/', views.add_vendor_document, name='add_vendor_document'),
    path('documents/<int:document_id>/edit/', views.edit_vendor_document, name='edit_vendor_document'),
    path('documents/<int:document_id>/delete/', views.delete_vendor_document, name='delete_vendor_document'),
    

    # Reports - New URLs
    path('reports/comprehensive/', views.comprehensive_reports, name='comprehensive_reports'),
    path('reports/vendor-performance/', views.vendor_performance_report, name='vendor_performance_report'),
    path('reports/project-status/', views.project_status_report, name='project_status_report'),
    path('reports/progress-summary/', views.progress_summary_report, name='progress_summary_report'),
    
    #User Profile - New URLs
   # path('profile/', views.user_profile, name='user_profile'),
    path('profile/settings/', views.user_settings, name='user_settings'),
    
   


    # API Endpoints - New URLs
    path('api/project/<int:project_id>/vendors/', views.project_vendors_api, name='project_vendors_api'),

    # Regional Performance URLs
    path('regional/performance/', views.regional_performance_list, name='regional_performance_list'),
    path('regional/performance/<int:performance_id>/', views.regional_performance_detail, name='regional_performance_detail'),
    path('regional/performance/<int:performance_id>/update/', views.regional_performance_update, name='regional_performance_update'),
    path('regional/performance/update-all/', views.update_all_regional_statistics, name='update_all_regional_statistics'),

    # Regional Project Template URLs
    path('regional/templates/', views.regional_template_list, name='regional_template_list'),
    path('regional/templates/<int:template_id>/', views.regional_template_detail, name='regional_template_detail'),
    path('regional/templates/create/', views.regional_template_create, name='regional_template_create'),
    path('regional/templates/<int:template_id>/update/', views.regional_template_update, name='regional_template_update'),
    path('regional/templates/<int:template_id>/assign-vendor/', views.assign_vendor_to_template, name='assign_vendor_to_template'),

    # Vendor Management URLs (for the Vendor model)
    path('vendor-management/', views.vendor_management_list, name='vendor_management_list'),
    path('vendor-management/<int:vendor_id>/', views.vendor_management_detail, name='vendor_management_detail'),
    path('vendor-management/available/', views.available_vendors_list, name='available_vendors_list'),

]
