{% extends "base.html" %}
{% load static %}

{% block content %}
<div class="container-fluid py-4">
  <div class="row">
    <div class="col-12">
      <div class="card shadow-sm">
        <div class="card-header bg-success text-white d-flex justify-content-between align-items-center">
          <h5 class="mb-0">
            <i class="bi bi-clipboard-check me-2"></i>Regional Project Templates
          </h5>
          <a href="{% url 'regional_template_create' %}" class="btn btn-light btn-sm">
            <i class="bi bi-plus-circle me-1"></i>Create New Template
          </a>
        </div>
        
        <div class="card-body">
          <!-- Filters -->
          <form method="get" class="row g-3 mb-4">
            <div class="col-md-3">
              <label for="region" class="form-label">Region</label>
              <select name="region" id="region" class="form-select">
                <option value="">All Regions</option>
                {% for region in regions %}
                <option value="{{ region.id }}" {% if selected_region == region.id|stringformat:"s" %}selected{% endif %}>
                  {{ region.name }}
                </option>
                {% endfor %}
              </select>
            </div>
            <div class="col-md-3">
              <label for="status" class="form-label">Status</label>
              <select name="status" id="status" class="form-select">
                <option value="">All Status</option>
                <option value="available" {% if selected_status == "available" %}selected{% endif %}>Available</option>
                <option value="assigned" {% if selected_status == "assigned" %}selected{% endif %}>Assigned</option>
                <option value="active" {% if selected_status == "active" %}selected{% endif %}>Active Project</option>
              </select>
            </div>
            <div class="col-md-3">
              <label for="activity" class="form-label">Main Activity</label>
              <select name="activity" id="activity" class="form-select">
                <option value="">All Activities</option>
                {% for activity in activities %}
                <option value="{{ activity.id }}" {% if selected_activity == activity.id|stringformat:"s" %}selected{% endif %}>
                  {{ activity.main_activity }}
                </option>
                {% endfor %}
              </select>
            </div>
            <div class="col-md-3">
              <label class="form-label">&nbsp;</label>
              <div class="d-grid">
                <button type="submit" class="btn btn-primary">
                  <i class="bi bi-funnel me-1"></i>Filter
                </button>
              </div>
            </div>
          </form>

          <!-- Templates Table -->
          <div class="table-responsive">
            <table class="table table-hover">
              <thead class="table-dark">
                <tr>
                  <th>Region</th>
                  <th>Main Activity</th>
                  <th>Status</th>
                  <th>Assigned Vendor</th>
                  <th>Project</th>
                  <th>Director Approval</th>
                  <th>Created</th>
                  <th>Actions</th>
                </tr>
              </thead>
              <tbody>
                {% for template in templates %}
                <tr>
                  <td>
                    <strong>{{ template.region.name }}</strong>
                    <br><small class="text-muted">{{ template.region.region_code }}</small>
                  </td>
                  <td>
                    <span class="badge bg-info">{{ template.main_activity.main_activity }}</span>
                  </td>
                  <td>
                    {% if template.status == 'available' %}
                      <span class="badge bg-success">Available</span>
                    {% elif template.status == 'assigned_pending' %}
                      <span class="badge bg-warning">Assigned Pending</span>
                    {% elif template.project %}
                      <span class="badge bg-primary">Active Project</span>
                    {% else %}
                      <span class="badge bg-secondary">Unknown</span>
                    {% endif %}
                  </td>
                  <td>
                    {% if template.assigned_vendor %}
                      <strong>{{ template.assigned_vendor.name }}</strong>
                      <br><small class="text-muted">Level {{ template.assigned_vendor.level }}</small>
                    {% else %}
                      <span class="text-muted">Not assigned</span>
                    {% endif %}
                  </td>
                  <td>
                    {% if template.project %}
                      <a href="#" class="text-decoration-none">{{ template.project.name }}</a>
                      <br><small class="text-muted">{{ template.project.status|title }}</small>
                    {% else %}
                      <span class="text-muted">No project</span>
                    {% endif %}
                  </td>
                  <td>
                    {% if template.approved_by_director %}
                      <span class="badge bg-success">
                        <i class="bi bi-check-circle me-1"></i>Approved
                      </span>
                      {% if template.director_approval_date %}
                        <br><small class="text-muted">{{ template.director_approval_date|date:"M d, Y" }}</small>
                      {% endif %}
                    {% else %}
                      <span class="badge bg-warning">
                        <i class="bi bi-clock me-1"></i>Pending
                      </span>
                    {% endif %}
                  </td>
                  <td>
                    <small>{{ template.created_at|date:"M d, Y" }}</small>
                  </td>
                  <td>
                    <div class="btn-group btn-group-sm">
                      <a href="{% url 'regional_template_detail' template.id %}" 
                         class="btn btn-outline-primary btn-sm">
                        <i class="bi bi-eye"></i>
                      </a>
                      <a href="{% url 'regional_template_update' template.id %}" 
                         class="btn btn-outline-secondary btn-sm">
                        <i class="bi bi-pencil"></i>
                      </a>
                      {% if template.can_assign_vendor %}
                      <a href="{% url 'assign_vendor_to_template' template.id %}" 
                         class="btn btn-outline-success btn-sm">
                        <i class="bi bi-person-plus"></i>
                      </a>
                      {% endif %}
                    </div>
                  </td>
                </tr>
                {% empty %}
                <tr>
                  <td colspan="8" class="text-center text-muted py-4">
                    <i class="bi bi-inbox display-4"></i>
                    <p class="mt-2">No regional project templates found</p>
                    <a href="{% url 'regional_template_create' %}" class="btn btn-primary">
                      <i class="bi bi-plus-circle me-1"></i>Create First Template
                    </a>
                  </td>
                </tr>
                {% endfor %}
              </tbody>
            </table>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
{% endblock %}
