{% extends "base.html" %}
{% load static %}

{% block content %}
<div class="container-fluid py-4">
  <div class="row">
    <div class="col-12">
      <div class="card shadow-sm">
        <div class="card-header bg-primary text-white d-flex justify-content-between align-items-center">
          <h5 class="mb-0">
            <i class="bi bi-graph-up me-2"></i>Regional Performance Dashboard
          </h5>
          <a href="{% url 'update_all_regional_statistics' %}" class="btn btn-light btn-sm">
            <i class="bi bi-arrow-clockwise me-1"></i>Update All Statistics
          </a>
        </div>
        
        <div class="card-body">
          <!-- Overall Statistics -->
          <div class="row mb-4">
            <div class="col-md-3">
              <div class="card bg-info text-white">
                <div class="card-body text-center">
                  <h4>{{ total_regions }}</h4>
                  <p class="mb-0">Total Regions</p>
                </div>
              </div>
            </div>
            <div class="col-md-3">
              <div class="card bg-primary text-white">
                <div class="card-body text-center">
                  <h4>{{ total_projects }}</h4>
                  <p class="mb-0">Total Projects</p>
                </div>
              </div>
            </div>
            <div class="col-md-3">
              <div class="card bg-success text-white">
                <div class="card-body text-center">
                  <h4>{{ total_completed }}</h4>
                  <p class="mb-0">Completed Projects</p>
                </div>
              </div>
            </div>
            <div class="col-md-3">
              <div class="card bg-warning text-white">
                <div class="card-body text-center">
                  <h4>{{ overall_completion_rate }}%</h4>
                  <p class="mb-0">Overall Completion Rate</p>
                </div>
              </div>
            </div>
          </div>

          <!-- Regional Performance Table -->
          <div class="table-responsive">
            <table class="table table-hover">
              <thead class="table-dark">
                <tr>
                  <th>Region</th>
                  <th>Total Projects</th>
                  <th>Completed</th>
                  <th>Active</th>
                  <th>Delayed</th>
                  <th>Completion Rate</th>
                  <th>Total Vendors</th>
                  <th>Active Vendors</th>
                  <th>Vendor Utilization</th>
                  <th>Avg Progress</th>
                  <th>Last Updated</th>
                  <th>Actions</th>
                </tr>
              </thead>
              <tbody>
                {% for performance in performances %}
                <tr>
                  <td>
                    <strong>{{ performance.region.name }}</strong>
                    <br><small class="text-muted">{{ performance.region.region_code }}</small>
                  </td>
                  <td>{{ performance.total_projects }}</td>
                  <td>
                    <span class="badge bg-success">{{ performance.completed_projects }}</span>
                  </td>
                  <td>
                    <span class="badge bg-primary">{{ performance.active_projects }}</span>
                  </td>
                  <td>
                    <span class="badge bg-warning">{{ performance.delayed_projects }}</span>
                  </td>
                  <td>
                    <div class="progress" style="height: 20px;">
                      <div class="progress-bar" role="progressbar" 
                           style="width: {{ performance.completion_rate }}%"
                           aria-valuenow="{{ performance.completion_rate }}" 
                           aria-valuemin="0" aria-valuemax="100">
                        {{ performance.completion_rate }}%
                      </div>
                    </div>
                  </td>
                  <td>{{ performance.total_vendors }}</td>
                  <td>
                    <span class="badge bg-info">{{ performance.active_vendors }}</span>
                  </td>
                  <td>
                    <div class="progress" style="height: 20px;">
                      <div class="progress-bar bg-info" role="progressbar" 
                           style="width: {{ performance.vendor_utilization_rate }}%"
                           aria-valuenow="{{ performance.vendor_utilization_rate }}" 
                           aria-valuemin="0" aria-valuemax="100">
                        {{ performance.vendor_utilization_rate }}%
                      </div>
                    </div>
                  </td>
                  <td>{{ performance.average_progress_percentage }}%</td>
                  <td>
                    <small>{{ performance.last_updated|date:"M d, Y H:i" }}</small>
                  </td>
                  <td>
                    <div class="btn-group btn-group-sm">
                      <a href="{% url 'regional_performance_detail' performance.id %}" 
                         class="btn btn-outline-primary btn-sm">
                        <i class="bi bi-eye"></i>
                      </a>
                      <a href="{% url 'regional_performance_update' performance.id %}" 
                         class="btn btn-outline-secondary btn-sm">
                        <i class="bi bi-pencil"></i>
                      </a>
                    </div>
                  </td>
                </tr>
                {% empty %}
                <tr>
                  <td colspan="12" class="text-center text-muted py-4">
                    <i class="bi bi-inbox display-4"></i>
                    <p class="mt-2">No regional performance data available</p>
                  </td>
                </tr>
                {% endfor %}
              </tbody>
            </table>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
{% endblock %}
