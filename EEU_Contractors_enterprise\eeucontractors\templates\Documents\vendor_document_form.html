{% extends 'base.html' %}

{% block content %}
<div class="container mt-4">
  <div class="card shadow-sm rounded-3 mb-4">
    <div class="card-header text-white text-center fw-bold" style="background: linear-gradient(135deg, #f68b1e, #28a745);">
      <h2 class="mb-0">{{ title }}</h2>
    </div>
    <div class="card-body">
      <form method="post" enctype="multipart/form-data" novalidate>
        {% csrf_token %}
        {{ form.non_field_errors }}
        
        {% for field in form.visible_fields %}
          <div class="mb-3">
            <label for="{{ field.id_for_label }}" class="form-label">{{ field.label }}</label>
            {{ field }}
            {% if field.help_text %}
              <div class="form-text">{{ field.help_text }}</div>
            {% endif %}
            {% for error in field.errors %}
              <div class="text-danger small">{{ error }}</div>
            {% endfor %}
          </div>
        {% endfor %}

        <div class="text-center">
          <button type="submit" class="btn btn-success">Submit</button>
        </div>
      </form>
    </div>
  </div>
</div>
{% endblock %}
<script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
<script>
  $(document).ready(function () {
    $('#id_vendor').change(function () {
      var vendorId = $(this).val();
      if (vendorId) {
        $.ajax({
          url: '/ajax/get-vendor-details/' + vendorId + '/',
          method: 'GET',
          success: function (data) {
            $('#id_vendor_id_number').val(data.vendor_id_number);
            $('#id_vendor_phone').val(data.vendor_phone);
            $('#id_vendor_level').val(data.vendor_level);
            $('#id_vendor_type').val(data.vendor_type);
          },
          error: function () {
            alert('Error loading vendor data.');
          }
        });
      } else {
        $('#id_vendor_id_number, #id_vendor_phone, #id_vendor_level, #id_vendor_type').val('');
      }
    });
  });
</script>


{% comment %} {% extends 'base.html' %}

{% block content %}
<div class="container mt-4">
  <div class="card shadow-sm rounded-3 mb-4">
    <div class="card-header text-white text-center fw-bold" style="background: linear-gradient(135deg, #f68b1e, #28a745);">
      <h2 class="mb-0">{{ title }}</h2>
    </div>
    <div class="card-body">
      <form method="post" enctype="multipart/form-data" novalidate>
        {% csrf_token %}
        {{ form.non_field_errors }}

        {% for field in form.visible_fields %}
          <div class="mb-3">
            <label for="{{ field.id_for_label }}" class="form-label">{{ field.label }}</label>
            {{ field }}
            {% if field.help_text %}
              <div class="form-text">{{ field.help_text }}</div>
            {% endif %}
            {% for error in field.errors %}
              <div class="text-danger small">{{ error }}</div>
            {% endfor %}
          </div>
        {% endfor %}

        <div class="text-center">
          <button type="submit" class="btn btn-success">
            Submit
          </button>
        </div>
      </form>
    </div>
  </div>
</div>
{% endblock %}
 {% endcomment %}





{% comment %} {% extends "base.html" %}
{% load static %}
{% block title %}{{ title }}{% endblock %}

{% block content %}
<style>
  /* Make Description textarea smaller width */
  textarea#id_description {
    max-width: 200px;  /* limit width */
    height: 80px;  /* reasonable height */
    resize: vertical;  /* allow vertical resize */
  }

  /* Style the card border */
  .card {
    border: 1px solid #ddd; /* light gray border */
    transition: border-color 0.3s ease;
  }

  /* Input, select, textarea default style */
  input.form-control, select.form-select, textarea.form-control {
    border: 1px solid #ccc;
    transition: border-color 0.3s ease;
  }

  /* Hover and focus effect for inputs */
  input.form-control:hover, select.form-select:hover, textarea.form-control:hover,
  input.form-control:focus, select.form-select:focus, textarea.form-control:focus {
    border-color: orange;
    box-shadow: 0 0 5px rgba(255, 165, 0, 0.5);
    outline: none;
  }
</style>

<div class="container mt-4" style="max-width: 900px;">
  <div class="card shadow-sm rounded-3 p-4">
    <h3 class="mb-4">{{ title }}</h3>

    <form method="post" enctype="multipart/form-data" novalidate>
      {% csrf_token %}

      <div class="row">
        <!-- Vendor Dropdown -->
        <div class="col-md-6 mb-3">
          <label for="id_vendor" class="form-label fw-semibold">Vendor:</label>
          {{ form.vendor }}
        </div>

        <!-- Display vendor info (read-only) -->
        {% if form.vendor.value %}
          <div class="col-md-6 mb-3">
            <label class="form-label fw-semibold">Selected Vendor Info:</label>
            <div class="card bg-light p-2">
              <small class="text-muted">
                ID: {{ form.instance.vendor.identification_number|default:"N/A" }}<br>
                Phone: {{ form.instance.vendor.phone_number|default:"N/A" }}<br>
                Type: {{ form.instance.vendor.get_contractor_type_display|default:"N/A" }}
              </small>
            </div>
          </div>
        {% endif %}
      </div>

      <div class="row">
        <!-- Render all other fields dynamically, excluding vendor field -->
        {% for field in form %}
          {% if field.name != 'vendor' %}
            <div class="col-md-6 mb-3">
              <label for="{{ field.id_for_label }}" class="form-label fw-semibold">{{ field.label }}</label>
              {{ field }}
              {% if field.help_text %}
                <small class="form-text text-muted">{{ field.help_text }}</small>
              {% endif %}
              {% for error in field.errors %}
                <div class="text-danger small mt-1">{{ error }}</div>
              {% endfor %}
            </div>
          {% endif %}
        {% endfor %}
      </div>

      <!-- Form buttons: Cancel and Save -->
      <div class="d-flex justify-content-between mt-4">
        <a href="{% url 'all_vendor_documents' %}" class="btn btn-secondary">
          <i class="bi bi-arrow-left"></i> Cancel
        </a>
        <button type="submit" class="btn btn-primary">
          <i class="bi bi-save"></i> Save
        </button>
      </div>
    </form>
  </div>
</div>

<!-- JavaScript removed - vendor info is displayed as read-only -->
{% endblock %}






{% endblock %} {% endcomment %}
