{% extends 'base.html' %}
{% load static %}

{% block title %}All Workloads Overview{% endblock %}

{% block content %}
<div class="container-fluid mt-4">
  <div class="d-flex justify-content-between align-items-center mb-4">
    <h2><i class="bi bi-clipboard-data me-2"></i>All Workloads Overview</h2>
    <a href="{% url 'project_progress_list' %}" class="btn btn-primary">
      <i class="bi bi-plus-circle"></i> Add New Project
    </a>
  </div>

  {% if project_data %}
    <!-- Statistics Cards -->
    <div class="row mb-4">
      <div class="col-md-3">
        <div class="card bg-primary text-white">
          <div class="card-body">
            <div class="d-flex justify-content-between">
              <div>
                <h4>{{ total_projects }}</h4>
                <p class="mb-0">Active Projects</p>
              </div>
              <div class="align-self-center">
                <i class="bi bi-folder fs-1"></i>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="col-md-3">
        <div class="card bg-success text-white">
          <div class="card-body">
            <div class="d-flex justify-content-between">
              <div>
                <h4>{{ project_data|length }}</h4>
                <p class="mb-0">Projects with Workloads</p>
              </div>
              <div class="align-self-center">
                <i class="bi bi-check-circle fs-1"></i>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Projects List -->
    {% for data in project_data %}
      <div class="card mb-4">
        <div class="card-header bg-light">
          <div class="row align-items-center">
            <div class="col-md-8">
              <h5 class="mb-0">
                <i class="bi bi-building me-2"></i>{{ data.project_progress.project.name }}
              </h5>
              <small class="text-muted">
                Vendor: {{ data.project_progress.vendor.name }} | 
                Main Activity: {{ data.project_progress.get_main_activity_display }}
              </small>
            </div>
            <div class="col-md-4 text-end">
              <div class="progress mb-1" style="height: 20px;">
                <div class="progress-bar bg-success" 
                     style="width: {{ data.avg_progress }}%;" 
                     role="progressbar">
                  {{ data.avg_progress }}%
                </div>
              </div>
              <small class="text-muted">Average Progress</small>
            </div>
          </div>
        </div>
        <div class="card-body">
          <div class="row mb-3">
            <div class="col-md-4">
              <div class="text-center">
                <h4 class="text-primary">{{ data.total_workloads }}</h4>
                <small class="text-muted">Total Workloads</small>
              </div>
            </div>
            <div class="col-md-4">
              <div class="text-center">
                <h4 class="text-success">{{ data.completed_workloads }}</h4>
                <small class="text-muted">Completed</small>
              </div>
            </div>
            <div class="col-md-4">
              <div class="text-center">
                <h4 class="text-warning">{{ data.total_workloads|add:"-"|add:data.completed_workloads }}</h4>
                <small class="text-muted">In Progress</small>
              </div>
            </div>
          </div>

          <!-- Sample Workloads -->
          {% if data.workloads %}
            <h6>Recent Workloads:</h6>
            <div class="table-responsive">
              <table class="table table-sm">
                <thead>
                  <tr>
                    <th>Sub Activity</th>
                    <th>Quantity</th>
                    <th>Progress</th>
                    <th>Actions</th>
                  </tr>
                </thead>
                <tbody>
                  {% for workload in data.workloads %}
                    <tr>
                      <td>{{ workload.sub_activity.name }}</td>
                      <td>{{ workload.completed_quantity }}/{{ workload.quantity }} {{ workload.unit_type }}</td>
                      <td>
                        {% with latest=workload.progress_records.last %}
                          {% if latest %}
                            <span class="badge bg-info">{{ latest.progress }}%</span>
                          {% else %}
                            <span class="badge bg-secondary">0%</span>
                          {% endif %}
                        {% endwith %}
                      </td>
                      <td>
                        <a href="{% url 'workload_detail' workload_id=workload.id %}" 
                           class="btn btn-sm btn-outline-primary">
                          <i class="bi bi-eye"></i>
                        </a>
                      </td>
                    </tr>
                  {% endfor %}
                </tbody>
              </table>
            </div>
          {% endif %}

          <!-- Action Buttons -->
          <div class="mt-3">
            <a href="{% url 'workload_list' progress_id=data.project_progress.id %}" 
               class="btn btn-primary btn-sm">
              <i class="bi bi-list"></i> View All Workloads
            </a>
            <a href="{% url 'add_workload' progress_id=data.project_progress.id %}" 
               class="btn btn-success btn-sm">
              <i class="bi bi-plus"></i> Add Workload
            </a>
          </div>
        </div>
      </div>
    {% endfor %}
  {% else %}
    <div class="alert alert-info text-center">
      <i class="bi bi-info-circle fs-1 mb-3"></i>
      <h4>No Workloads Found</h4>
      <p>No projects with workloads have been created yet.</p>
      <a href="{% url 'project_progress_list' %}" class="btn btn-primary">
        <i class="bi bi-plus-circle"></i> Create Your First Project
      </a>
    </div>
  {% endif %}
</div>
{% endblock %}
