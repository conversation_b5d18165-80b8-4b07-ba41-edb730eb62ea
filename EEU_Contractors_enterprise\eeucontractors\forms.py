from django import forms
from django.core.exceptions import ValidationError
from django.utils import timezone
from .models import (
    Vendorsregistration, Project, Vendor, Region, SubActivityWorkload,
    SubActivityProgress, ProjectVendorsHistory, VendorDocument, SubActivity,
    RegionalPerformance, RegionalProjectTemplate
)
from django.forms import DateInput

# class VendorForm(forms.ModelForm):
#     class Meta:
#         model = Vendor
#         fields = ['vendor_registration', 'phone_number', 'year', 'experience']
#     def clean_vendor_registration(self):
#         vendor_registration = self.cleaned_data['vendor_registration']
#         VendorModel = Vendor
#         # Check if another vendor already registered with this vendor_registration
#         if VendorModel.objects.filter(vendor_registration=vendor_registration).exists():
#             raise forms.ValidationError("A vendor is already registered with this vendor registration.")
#       # Check if there's an active project for this vendor_registration
#         from .models import Project
#         if Project.objects.filter(vendor__vendor_registration=vendor_registration, status='active').exists():
#             raise forms.ValidationError("An active project is already associated with this vendor registration.")
#         return vendor_registration
class VendorsRegistrationForm(forms.ModelForm):
    class Meta:
        model = Vendorsregistration
        fields = [
            'region',  # 👈 Include region here
            'contractor_type', 'contractor_name', 'phone_number', 'experience', 'email',
            'has_professional_license', 'electric_grade', 'has_human_resource',
            'electrical_engineers', 'electromechanical_engineers', 'civil_engineers',
            'electricians_foremen', 'surveyors', 'line_workers',
            'has_work_experience_doc', 'has_trade_license', 'has_energy_certification',
            'has_equipment_resources', 'has_vat_and_tax', 'has_good_performance',
            'has_financial_capacity', 'registrar_name', 'registrar_id'
        ]
        # identification_number and created_at are excluded because they're auto-populated
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)      
        # Set default contractor_type to 'new_enterprise'
        self.fields['contractor_type'].initial = 'new_enterprise'
                # Make some fields required
        self.fields['registrar_name'].required = True
        self.fields['registrar_id'].required = True
        self.fields['phone_number'].required = True
        self.fields['experience'].required = True
        self.fields['email'].required = True
                # Add Bootstrap classes to all fields
        for field_name, field in self.fields.items():
            if isinstance(field.widget, forms.CheckboxInput):
                field.widget.attrs['class'] = 'form-check-input'
            else:
                field.widget.attrs['class'] = 'form-control'
                # Add placeholder text
            if field_name == 'contractor_name':
                field.widget.attrs['placeholder'] = 'Enter contractor name'
            elif field_name == 'phone_number':
                field.widget.attrs['placeholder'] = 'Enter phone number (e.g., 0911234567)'
            elif field_name == 'experience':
                field.widget.attrs['placeholder'] = 'Years of experience (e.g., 5)'
            elif field_name == 'email':
                field.widget.attrs['placeholder'] = 'Enter email address'
            elif field_name == 'registrar_name':
                field.widget.attrs['placeholder'] = 'Enter your name'
            elif field_name == 'registrar_id':
                field.widget.attrs['placeholder'] = 'Enter your ID'
        # Add help text for some fields
        self.fields['electric_grade'].help_text = 'Select the appropriate electric grade level'
        self.fields['has_human_resource'].help_text = 'Check if all required human resources are available'
            # Group number fields
        for field_name in ['electrical_engineers', 'electromechanical_engineers', 'civil_engineers',
                          'electricians_foremen', 'surveyors', 'line_workers']:
            self.fields[field_name].widget.attrs['min'] = 0
    def clean(self):
        cleaned_data = super().clean()     
        # Validate that if has_human_resource is True, at least one engineer type has a value > 0
        has_human_resource = cleaned_data.get('has_human_resource')
        if has_human_resource:
            engineers_count = (
                cleaned_data.get('electrical_engineers', 0) +
                cleaned_data.get('electromechanical_engineers', 0) +
                cleaned_data.get('civil_engineers', 0)
            )
    
            if engineers_count == 0:
                raise forms.ValidationError(
                    "If human resources are available, at least one engineer type must be specified."
                )
                
        # Validate electric_grade is provided if has_professional_license is True
        has_professional_license = cleaned_data.get('has_professional_license')
        electric_grade = cleaned_data.get('electric_grade')     
        if has_professional_license and not electric_grade:
            self.add_error(
                'electric_grade',
                "Electric grade is required when professional license is available."
            )
            
        return cleaned_data
# Add this new form for advanced filtering
class VendorFilterForm(forms.Form):
    CONTRACTOR_TYPE_CHOICES = [
        ('all', 'All Types'),
        ('contractor', 'Contractor'),
        ('enterprise', 'Enterprise'),
        ('new_enterprise', 'New Enterprise'),
    ]

    
    DATE_FILTER_CHOICES = [
        ('any', 'Any Time'),
        ('today', 'Today'),
        ('this_week', 'This Week'),
        ('this_month', 'This Month'),
        ('custom', 'Custom Range'),
    ]
    
    contractor_type = forms.ChoiceField(
        choices=CONTRACTOR_TYPE_CHOICES,
        required=False,
        widget=forms.Select(attrs={'class': 'form-select'})
    )
    

    
    date_filter = forms.ChoiceField(
        choices=DATE_FILTER_CHOICES,
        required=False,
        widget=forms.Select(attrs={'class': 'form-select'})
    )
    
    start_date = forms.DateField(
        required=False,
        widget=forms.DateInput(attrs={'class': 'form-control', 'type': 'date'})
    )
    
    end_date = forms.DateField(
        required=False,
        widget=forms.DateInput(attrs={'class': 'form-control', 'type': 'date'})
    )
    
    search = forms.CharField(
        required=False,
        widget=forms.TextInput(attrs={
            'class': 'form-control',
            'placeholder': 'Search by name, ID or registrar'
        })
    )
    
    def clean(self):
        cleaned_data = super().clean()
        date_filter = cleaned_data.get('date_filter')
        start_date = cleaned_data.get('start_date')
        end_date = cleaned_data.get('end_date')
        
        if date_filter == 'custom':
            if not start_date:
                self.add_error('start_date', 'Start date is required for custom date range')
            if not end_date:
                self.add_error('end_date', 'End date is required for custom date range')
            if start_date and end_date and start_date > end_date:
                self.add_error('end_date', 'End date must be after start date')
                
        return cleaned_data
class ProjectForm(forms.ModelForm):
    class Meta:
        model = Project
        fields = '__all__'
        widgets = {
            'start_date': DateInput(attrs={'type': 'date'}),
            'estimated_end_date': DateInput(attrs={'type': 'date'}),
            'end_date': DateInput(attrs={'type': 'date'}),
            'approved_date': DateInput(attrs={'type': 'date'}),
        }

    def __init__(self, *args, **kwargs):
        user = kwargs.pop('user', None)
        super().__init__(*args, **kwargs)
        
        for field in self.fields.values():
            field.widget.attrs.update({'class': 'form-control'})

        self.fields['level'].help_text = 'Level must match vendor capabilities and activity requirements.'
        self.fields['main_activity'].help_text = 'Select the primary activity for this project.'

        if self.instance and self.instance.vendor_id:
            try:
                vendor = self.instance.vendor
                self.fields['level'].initial = vendor.level
            except Exception:
                pass

        if user and not user.is_superuser:
            self.fields['region'].queryset = Region.objects.filter(id=user.region_id)

    def clean(self):
        cleaned_data = super().clean()
        vendor = cleaned_data.get('vendor')
        level = cleaned_data.get('level')
        main_activity = cleaned_data.get('main_activity')

        if vendor and level:
            if str(vendor.level) != str(level):
                self.add_error(
                    'level',
                    f"Vendor level in form ({level}) doesn't match vendor's actual level ({vendor.level})."
                )

        if main_activity and level:
            try:
                level_int = int(level)
            except (ValueError, TypeError):
                self.add_error('level', "Invalid level format.")
                return cleaned_data

            required_levels = main_activity.get_required_levels()

            level_map = {
                'level_1_2_3': {1, 2, 3},
                'level_4_5_6': {4, 5, 6},
                'level_7_8': {7, 8},
            }

            allowed_levels = set()
            for group in required_levels:
                allowed_levels.update(level_map.get(group, set()))

            if level_int not in allowed_levels:
                self.add_error(
                    'main_activity',
                    f"Vendor level {level_int} is not suitable for this activity. Allowed levels: {sorted(allowed_levels)}."
                )
        return cleaned_data
from django import forms
from django.forms import inlineformset_factory, DateInput
from .models import (
    SubActivityWorkload, SubActivityProgress, ProjectVendorsHistory,
    VendorDocument, SubActivity, ProjectProgress
)
# Date picker widget
class DatePickerInput(DateInput):
    input_type = 'date'

# ProjectProgress Form
class ProjectProgressForm(forms.ModelForm):
    class Meta:
        model = ProjectProgress
        fields = ['project', 'vendor', 'main_activity', 'start_date', 'progress_percentage', 'completion_date', 'status', 'remarks']
        widgets = {
            'start_date': DatePickerInput(),
            'completion_date': DatePickerInput(),
            'remarks': forms.Textarea(attrs={'rows': 3}),
        }

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        for visible in self.visible_fields():
            visible.field.widget.attrs['class'] = 'form-control'

        # Add help text
        self.fields['progress_percentage'].help_text = 'Overall progress percentage (0-100)'
        self.fields['status'].help_text = 'Current status of the project'

        # Debug: Check if data exists
        from .models import Project, Vendor, MainActivity
        project_count = Project.objects.count()
        vendor_count = Vendor.objects.count()
        activity_count = MainActivity.objects.count()

        if project_count == 0:
            self.fields['project'].help_text = 'No projects available. Please create projects first.'
        if vendor_count == 0:
            self.fields['vendor'].help_text = 'No vendors available. Please create vendors first.'
        if activity_count == 0:
            self.fields['main_activity'].help_text = 'No main activities available. Please create main activities first.'

    def clean(self):
        cleaned_data = super().clean()
        start_date = cleaned_data.get('start_date')
        completion_date = cleaned_data.get('completion_date')
        progress_percentage = cleaned_data.get('progress_percentage')
        status = cleaned_data.get('status')
        project = cleaned_data.get('project')
        vendor = cleaned_data.get('vendor')
        main_activity = cleaned_data.get('main_activity')

        # Validate date logic
        if start_date and completion_date and start_date > completion_date:
            raise forms.ValidationError("Completion date cannot be earlier than start date.")

        # Validate progress percentage
        if progress_percentage is not None:
            if progress_percentage < 0 or progress_percentage > 100:
                raise forms.ValidationError("Progress percentage must be between 0 and 100.")

        # Validate completion logic
        if status == 'completed':
            if not completion_date:
                raise forms.ValidationError("Completion date is required when status is 'completed'.")
            if progress_percentage and progress_percentage < 100:
                raise forms.ValidationError("Progress must be 100% when status is 'completed'.")

        # Validate vendor and project relationship
        if project and vendor:
            if project.vendor != vendor:
                raise forms.ValidationError("Selected vendor must match the project's assigned vendor.")

        # Validate main activity compatibility
        if project and main_activity:
            if project.main_activity != main_activity:
                raise forms.ValidationError("Main activity must match the project's main activity.")

        return cleaned_data
class SubActivityWorkloadForm(forms.ModelForm):
    class Meta:
        model = SubActivityWorkload
        fields = ['sub_activity', 'quantity', 'completed_quantity', 'unit_type', 'expected_duration']
        widgets = {
            'sub_activity': forms.Select(attrs={'class': 'form-control'}),
            'quantity': forms.NumberInput(attrs={'class': 'form-control', 'min': '1', 'placeholder': 'Total quantity'}),
            'completed_quantity': forms.NumberInput(attrs={'class': 'form-control', 'min': '0', 'placeholder': 'Completed quantity'}),
            'unit_type': forms.TextInput(attrs={'class': 'form-control', 'placeholder': 'e.g., poles, meters, transformers'}),
            'expected_duration': forms.NumberInput(attrs={
                'class': 'form-control',
                'readonly': True,
                'title': 'Auto-calculated: base_duration × quantity'
            })
        }

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        # Make expected_duration field read-only and add help text
        self.fields['expected_duration'].help_text = 'Auto-calculated: base_duration × quantity'
        self.fields['expected_duration'].widget.attrs['readonly'] = True

        # Add validation help text for quantity
        self.fields['quantity'].help_text = 'Changing quantity will automatically recalculate expected duration'

    def clean(self):
        cleaned_data = super().clean()
        sub_activity = cleaned_data.get('sub_activity')
        quantity = cleaned_data.get('quantity')

        # Validate that we have both sub_activity and quantity for duration calculation
        if sub_activity and quantity:
            # Calculate expected duration to show user what it will be
            calculated_duration = int(float(sub_activity.base_duration) * quantity)
            if calculated_duration < 1:
                calculated_duration = 1

            # Update the expected_duration in cleaned_data
            cleaned_data['expected_duration'] = calculated_duration

        return cleaned_data

    def __init__(self, *args, **kwargs):
        project_progress = kwargs.pop('project_progress', None)
        super().__init__(*args, **kwargs)

        # Add help text
        self.fields['quantity'].help_text = 'Total quantity of work units to be completed'
        self.fields['completed_quantity'].help_text = 'Number of units already completed'
        self.fields['unit_type'].help_text = 'Type of units (poles, meters, transformers, etc.)'
        self.fields['expected_duration'].help_text = 'Expected duration in days to complete this workload'

        if project_progress:
            # Filter sub_activity choices based on the main_activity of the project_progress
            if project_progress.main_activity:
                self.fields['sub_activity'].queryset = self.fields['sub_activity'].queryset.filter(
                    main_activity=project_progress.main_activity
                )

        # Add JavaScript to auto-fill expected_duration based on selected sub_activity
        self.fields['sub_activity'].widget.attrs.update({
            'onchange': 'updateExpectedDuration(this)'
        })

    def clean(self):
        cleaned_data = super().clean()
        quantity = cleaned_data.get('quantity')
        completed_quantity = cleaned_data.get('completed_quantity')
        expected_duration = cleaned_data.get('expected_duration')

        # Validate completed_quantity doesn't exceed total quantity
        if quantity and completed_quantity and completed_quantity > quantity:
            raise forms.ValidationError(
                f"Completed quantity ({completed_quantity}) cannot exceed total quantity ({quantity})."
            )

        # Validate expected_duration is reasonable
        if expected_duration and expected_duration <= 0:
            raise forms.ValidationError("Expected duration must be at least 1 day.")

        return cleaned_data

class SubActivityProgressForm(forms.ModelForm):
    class Meta:
        model = SubActivityProgress
        fields = ['progress', 'start_date', 'end_date']
        widgets = {
            'progress': forms.NumberInput(attrs={
                'class': 'form-control',
                'min': '0',
                'max': '100',
                'step': '1',
                'required': 'required',
                'placeholder': 'Enter progress %'
            }),
            'start_date': forms.DateInput(attrs={
                'class': 'form-control',
                'type': 'date',
                'required': 'required'
            }),
            'end_date': forms.DateInput(attrs={
                'class': 'form-control',
                'type': 'date',
                'required': 'required'
            }),
        }

    def __init__(self, *args, **kwargs):
        self.workload = kwargs.pop('workload', None)
        super().__init__(*args, **kwargs)

        # Add help text
        self.fields['progress'].help_text = 'Progress percentage for this time period (0-100%)'
        self.fields['start_date'].help_text = 'Start date of this progress period'
        self.fields['end_date'].help_text = 'End date of this progress period'

    def clean(self):
        cleaned_data = super().clean()
        start_date = cleaned_data.get('start_date')
        end_date = cleaned_data.get('end_date')
        progress = cleaned_data.get('progress')

        # Validate date logic
        if start_date and end_date and start_date > end_date:
            raise forms.ValidationError("End date cannot be earlier than start date.")

        # Validate progress is reasonable
        if progress is not None:
            if progress < 0 or progress > 100:
                raise forms.ValidationError("Progress must be between 0 and 100.")

        # Validate progress doesn't decrease (unless it's a correction for earlier date)
        if self.workload and hasattr(self, 'instance') and self.instance.pk:
            latest_progress = self.workload.progress_records.exclude(pk=self.instance.pk).order_by('-end_date').first()
            if latest_progress and progress and end_date:
                if progress < latest_progress.progress and end_date > latest_progress.end_date:
                    raise forms.ValidationError(
                        f'Progress cannot decrease from {latest_progress.progress}% to {progress}% '
                        f'unless this is a correction for an earlier date.'
                    )

        return cleaned_data

# ProjectVendorsHistory Form
class ProjectVendorsHistoryForm(forms.ModelForm):
    class Meta:
        model = ProjectVendorsHistory
        fields = ['vendor', 'project', 'status', 'start_date', 'end_date', 'cause_of_delay_or_suspension', 'evaluation_notes']
        widgets = {
            'start_date': DatePickerInput(),
            'end_date': DatePickerInput(),
            'evaluation_notes': forms.Textarea(attrs={'rows': 4, 'class': 'form-control'}),
            'cause_of_delay_or_suspension': forms.Select(attrs={'class': 'form-control'}),
        }

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        for visible in self.visible_fields():
            visible.field.widget.attrs['class'] = 'form-control'

        # Add help text
        self.fields['status'].help_text = 'Final status of the vendor on this project'
        self.fields['cause_of_delay_or_suspension'].help_text = 'Required if status is delayed or suspended'
        self.fields['evaluation_notes'].help_text = 'Additional notes about vendor performance'

    def clean(self):
        cleaned_data = super().clean()
        start_date = cleaned_data.get('start_date')
        end_date = cleaned_data.get('end_date')
        status = cleaned_data.get('status')
        cause_of_delay_or_suspension = cleaned_data.get('cause_of_delay_or_suspension')

        # Validate date logic
        if start_date and end_date and start_date > end_date:
            raise forms.ValidationError("End date cannot be earlier than start date.")

        # Validate cause of delay/suspension is provided when needed
        if status in ['delayed', 'suspended'] and not cause_of_delay_or_suspension:
            raise forms.ValidationError("You must specify the cause of delay or suspension.")

        # Clear cause if status is completed
        if status == 'completed':
            cleaned_data['cause_of_delay_or_suspension'] = None

        return cleaned_data


# Define the formsets
SubActivityProgressFormSet = inlineformset_factory(
    SubActivityWorkload,  # parent model
    SubActivityProgress,  # child model
    form=SubActivityProgressForm,
    fields=('progress', 'start_date', 'end_date'),
    extra=1,
    can_delete=True,
    fk_name='workload'  # Explicitly specify the foreign key field name
)

SubActivityWorkloadFormSet = inlineformset_factory(
    ProjectProgress,  # parent model
    SubActivityWorkload,  # child model
    form=SubActivityWorkloadForm,
    fields=('sub_activity', 'quantity', 'completed_quantity', 'unit_type', 'expected_duration'),
    extra=1,
    can_delete=True,
    fk_name='project_progress'
)


# # VendorDocument Form
# class VendorDocumentForm(forms.ModelForm):
#     class Meta:
#         model = VendorDocument
#         fields = ['vendor', 'document_type', 'file', 'description', 'expiry_date']
#         widgets = {
#             'expiry_date': DatePickerInput(),
#             'description': forms.Textarea(attrs={'rows': 3, 'class': 'form-control'}),
#             'document_type': forms.Select(attrs={'class': 'form-control'}),
#             'file': forms.FileInput(attrs={'class': 'form-control', 'accept': '.pdf,.doc,.docx,.jpg,.jpeg,.png'}),
#         }

#     def __init__(self, *args, **kwargs):
#         super().__init__(*args, **kwargs)
#         for visible in self.visible_fields():
#             if visible.field.widget.__class__.__name__ != 'FileInput':
#                 visible.field.widget.attrs['class'] = 'form-control'

#         # Add help text
#         self.fields['document_type'].help_text = 'Type of document being uploaded'
#         self.fields['file'].help_text = 'Upload document file (PDF, DOC, or image formats)'
#         self.fields['description'].help_text = 'Brief description of the document'
#         self.fields['expiry_date'].help_text = 'Document expiry date (if applicable)'

#     def clean_file(self):
#         file = self.cleaned_data.get('file')
#         if file:
#             # Validate file size (max 10MB)
#             if file.size > 10 * 1024 * 1024:
#                 raise forms.ValidationError("File size cannot exceed 10MB.")

#             # Validate file extension
#             allowed_extensions = ['.pdf', '.doc', '.docx', '.jpg', '.jpeg', '.png']
#             file_extension = file.name.lower().split('.')[-1]
#             if f'.{file_extension}' not in allowed_extensions:
#                 raise forms.ValidationError(
#                     f"File type not allowed. Allowed types: {', '.join(allowed_extensions)}"
#                 )
#         return file
class ProjectVendorsHistoryForm(forms.ModelForm):
    class Meta:
        model = ProjectVendorsHistory
        fields = [
            'vendor', 'project', 'status', 'evaluation_notes', 
            'start_date', 'end_date', 'cause_of_delay_or_suspension'
        ]
        widgets = {
            'vendor': forms.Select(attrs={'class': 'form-control'}),
            'project': forms.Select(attrs={'class': 'form-control'}),
            'status': forms.Select(attrs={'class': 'form-control'}),
            'evaluation_notes': forms.Textarea(attrs={'class': 'form-control', 'rows': 3}),
            'start_date': DatePickerInput(attrs={'class': 'form-control'}),
            'end_date': DatePickerInput(attrs={'class': 'form-control'}),
            'cause_of_delay_or_suspension': forms.Select(attrs={'class': 'form-control'}),
        }
    
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        # Make cause_of_delay_or_suspension initially hidden
        self.fields['cause_of_delay_or_suspension'].widget.attrs['class'] += ' delay-reason-field'
        self.fields['cause_of_delay_or_suspension'].widget.attrs['id'] = 'delay_reason_field'


class VendorDocumentForm(forms.ModelForm):
    vendor_id_number = forms.CharField(required=False, label="ID Number", widget=forms.TextInput(attrs={'readonly': 'readonly'}))
    vendor_phone = forms.CharField(required=False, label="Phone", widget=forms.TextInput(attrs={'readonly': 'readonly'}))
    vendor_level = forms.CharField(required=False, label="Level", widget=forms.TextInput(attrs={'readonly': 'readonly'}))
    vendor_type = forms.CharField(required=False, label="Type", widget=forms.TextInput(attrs={'readonly': 'readonly'}))

    class Meta:
        model = VendorDocument
        fields = ['vendor', 'vendor_id_number', 'vendor_phone', 'vendor_level', 'vendor_type', 'document_type', 'file', 'description', 'expiry_date']
        widgets = {
            'expiry_date': forms.DateInput(attrs={'type': 'date', 'class': 'form-control'}),
        }
# # forms.py
# from django import forms
# from .models import VendorDocument
# class VendorDocumentForm(forms.ModelForm):
#     vendor_id_number = forms.CharField(required=False, label="ID Number", widget=forms.TextInput(attrs={'readonly': 'readonly'}))
#     vendor_phone = forms.CharField(required=False, label="Phone", widget=forms.TextInput(attrs={'readonly': 'readonly'}))
#     vendor_level = forms.CharField(required=False, label="Level", widget=forms.TextInput(attrs={'readonly': 'readonly'}))
#     vendor_type = forms.CharField(required=False, label="Type", widget=forms.TextInput(attrs={'readonly': 'readonly'}))

#     class Meta:
#         model = VendorDocument
#         fields = ['vendor', 'vendor_id_number', 'vendor_phone', 'vendor_level', 'vendor_type', 'document_type', 'file', 'description', 'expiry_date']
#         widgets = {
#             'expiry_date': forms.DateInput(attrs={'type': 'date', 'class': 'form-control'}),
#         }
# Regional Performance Form
class RegionalPerformanceForm(forms.ModelForm):
    """Form for viewing and updating regional performance metrics"""

    class Meta:
        model = RegionalPerformance
        fields = [
            'region', 'total_projects', 'completed_projects', 'active_projects', 'delayed_projects',
            'total_vendors', 'active_vendors', 'blacklisted_vendors',
            'average_project_completion_time', 'average_progress_percentage',
            'total_project_value', 'completed_project_value'
        ]
        widgets = {
            'region': forms.Select(attrs={'class': 'form-control'}),
            'total_projects': forms.NumberInput(attrs={'class': 'form-control', 'readonly': True}),
            'completed_projects': forms.NumberInput(attrs={'class': 'form-control', 'readonly': True}),
            'active_projects': forms.NumberInput(attrs={'class': 'form-control', 'readonly': True}),
            'delayed_projects': forms.NumberInput(attrs={'class': 'form-control', 'readonly': True}),
            'total_vendors': forms.NumberInput(attrs={'class': 'form-control', 'readonly': True}),
            'active_vendors': forms.NumberInput(attrs={'class': 'form-control', 'readonly': True}),
            'blacklisted_vendors': forms.NumberInput(attrs={'class': 'form-control', 'readonly': True}),
            'average_project_completion_time': forms.NumberInput(attrs={'class': 'form-control', 'readonly': True}),
            'average_progress_percentage': forms.NumberInput(attrs={'class': 'form-control', 'readonly': True}),
            'total_project_value': forms.NumberInput(attrs={'class': 'form-control', 'readonly': True}),
            'completed_project_value': forms.NumberInput(attrs={'class': 'form-control', 'readonly': True}),
        }

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        # Make most fields readonly as they are calculated automatically
        readonly_fields = [
            'total_projects', 'completed_projects', 'active_projects', 'delayed_projects',
            'total_vendors', 'active_vendors', 'blacklisted_vendors',
            'average_project_completion_time', 'average_progress_percentage',
            'total_project_value', 'completed_project_value'
        ]

        for field_name in readonly_fields:
            if field_name in self.fields:
                self.fields[field_name].widget.attrs['readonly'] = True
                self.fields[field_name].disabled = True


# Regional Project Template Form (Enhanced)
class RegionalProjectTemplateForm(forms.ModelForm):
    """Form for managing regional project templates"""

    class Meta:
        model = RegionalProjectTemplate
        fields = [
            'region', 'main_activity', 'is_available', 'assigned_vendor', 'project',
            'approved_by_director', 'director_approval_date', 'director_notes'
        ]
        widgets = {
            'region': forms.Select(attrs={'class': 'form-control'}),
            'main_activity': forms.Select(attrs={'class': 'form-control'}),
            'is_available': forms.CheckboxInput(attrs={'class': 'form-check-input'}),
            'assigned_vendor': forms.Select(attrs={'class': 'form-control'}),
            'project': forms.Select(attrs={'class': 'form-control'}),
            'approved_by_director': forms.CheckboxInput(attrs={'class': 'form-check-input'}),
            'director_approval_date': forms.DateTimeInput(attrs={
                'class': 'form-control',
                'type': 'datetime-local'
            }),
            'director_notes': forms.Textarea(attrs={
                'class': 'form-control',
                'rows': 3,
                'placeholder': 'Director notes about this project assignment...'
            }),
        }

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)

        # Filter vendors to only show active ones
        if 'assigned_vendor' in self.fields:
            self.fields['assigned_vendor'].queryset = Vendor.objects.filter(status='active')
            self.fields['assigned_vendor'].empty_label = "Select Vendor"

        # Filter projects to only show active ones
        if 'project' in self.fields:
            self.fields['project'].queryset = Project.objects.filter(status__in=['active', 'pending'])
            self.fields['project'].empty_label = "Select Project"

    def clean(self):
        cleaned_data = super().clean()
        assigned_vendor = cleaned_data.get('assigned_vendor')
        project = cleaned_data.get('project')

        # Validate vendor-project consistency
        if assigned_vendor and project and project.vendor != assigned_vendor:
            raise ValidationError("Assigned vendor must match the project vendor.")

        # Validate vendor can take new project
        if assigned_vendor and hasattr(assigned_vendor, 'can_take_new_project'):
            if not assigned_vendor.can_take_new_project():
                raise ValidationError({
                    'assigned_vendor': "This vendor cannot take a new project."
                })

        return cleaned_data
