from django.contrib import admin

# Register your models here.
from django.contrib import admin
from django.utils.html import format_html
from django.urls import reverse
from django.utils.safestring import mark_safe
from django.db.models import Count, Q
from django.contrib import messages
from django.utils import timezone
from .defaults import DEFAULT_SUBACTIVITIES
from .models import (
    EEUHeadOffice, Region, CustomUser, Vendorsregistration, Vendor,
    MainActivity, SubActivity, Project, ProjectProgress, SubActivityWorkload,
    SubActivityProgress, ProjectVendorsHistory, VendorDocument,
    RegionalPerformance, RegionalProjectTemplate
)

# ============================================================================
# ORGANIZATIONAL STRUCTURE ADMIN
# ============================================================================

@admin.register(EEUHeadOffice)
class EEUHeadOfficeAdmin(admin.ModelAdmin):
    list_display = ['name']
    readonly_fields = ['name']

@admin.register(Region)
class RegionAdmin(admin.ModelAdmin):
    list_display = ['name', 'region_code', 'total_vendors_count', 'active_projects_count']
    list_filter = ['region_code']
    search_fields = ['name', 'region_code']
    ordering = ['region_code', 'name']

    def total_vendors_count(self, obj):
        return obj.total_vendors
    total_vendors_count.short_description = 'Total Vendors'

    def active_projects_count(self, obj):
        return obj.active_projects
    active_projects_count.short_description = 'Active Projects'

@admin.register(CustomUser)
class CustomUserAdmin(admin.ModelAdmin):
    list_display = ['username', 'first_name', 'last_name', 'role', 'region', 'is_active']
    list_filter = ['role', 'region', 'is_active', 'is_staff']
    search_fields = ['username', 'first_name', 'last_name', 'email']
    fieldsets = (
        (None, {'fields': ('username', 'password')}),
        ('Personal info', {'fields': ('first_name', 'last_name', 'email')}),
        ('Organizational', {'fields': ('role', 'region')}),
        ('Permissions', {'fields': ('is_active', 'is_staff', 'is_superuser', 'groups', 'user_permissions')}),
        ('Important dates', {'fields': ('last_login', 'date_joined')}),
    )

# ============================================================================
# VENDOR REGISTRATION & MANAGEMENT ADMIN
# ============================================================================

@admin.register(Vendorsregistration)
class VendorsregistrationAdmin(admin.ModelAdmin):
    list_display = [
        'contractor_name', 'contractor_type', 'electric_grade', 'region',
        'phone_number', 'experience', 'email', 'registration_date', 'compliance_status'
    ]
    list_filter = [
        'contractor_type', 'electric_grade', 'region', 'registration_date',
        'has_professional_license', 'has_trade_license'
    ]
    search_fields = ['contractor_name', 'identification_number', 'phone_number', 'email']
    readonly_fields = ['identification_number', 'registration_date', 'created_at']
    date_hierarchy = 'registration_date'

    fieldsets = (
        ('Basic Information', {
            'fields': ('contractor_type', 'contractor_name', 'region', 'phone_number', 'experience', 'email')
        }),
        ('Professional Details', {
            'fields': ('electric_grade', 'has_professional_license', 'has_human_resource')
        }),
        ('Staff Resources', {
            'fields': ('electrical_engineers', 'electromechanical_engineers', 'civil_engineers',
                      'electricians_foremen', 'surveyors', 'line_workers'),
            'classes': ('collapse',)
        }),
        ('Compliance Documents', {
            'fields': ('has_work_experience_doc', 'has_trade_license', 'has_energy_certification',
                      'has_equipment_resources', 'has_vat_and_tax', 'has_good_performance', 'has_financial_capacity'),
            'classes': ('collapse',)
        }),
        ('Registration Details', {
            'fields': ('registrar_name', 'registrar_id', 'identification_number', 'registration_date'),
            'classes': ('collapse',)
        }),
    )

    def compliance_status(self, obj):
        compliance_fields = [
            obj.has_professional_license, obj.has_work_experience_doc, obj.has_trade_license,
            obj.has_energy_certification, obj.has_equipment_resources, obj.has_vat_and_tax,
            obj.has_good_performance, obj.has_financial_capacity
        ]
        completed = sum(compliance_fields)
        total = len(compliance_fields)
        percentage = (completed / total) * 100

        if percentage == 100:
            color = 'green'
        elif percentage >= 70:
            color = 'orange'
        else:
            color = 'red'

        return format_html(
            '<span style="color: {};">{}/{} ({}%)</span>',
            color, completed, total, int(percentage)
        )
    compliance_status.short_description = 'Compliance Status'
@admin.register(Vendor)
class VendorAdmin(admin.ModelAdmin):
    list_display = [
        'name', 'type', 'level', 'region_name', 'status', 'phone_number',
        'email', 'current_project_link', 'total_projects_count'
    ]
    list_filter = ['type', 'level', 'status', 'vendor_registration__region']
    search_fields = ['name', 'identification_number', 'phone_number', 'email']
    readonly_fields = [
        'name', 'type', 'level', 'identification_number', 'phone_number',
        'experience', 'email', 'status', 'vendor_classification_display'
    ]

    fieldsets = (
        ('Vendor Information', {
            'fields': ('vendor_registration', 'name', 'type', 'level', 'identification_number')
        }),
        ('Contact Information', {
            'fields': ('phone_number', 'email', 'experience')
        }),
        ('Status & Classification', {
            'fields': ('status', 'vendor_classification_display')
        }),
    )

    def region_name(self, obj):
        return obj.vendor_registration.region.name if obj.vendor_registration else 'N/A'
    region_name.short_description = 'Region'

    def current_project_link(self, obj):
        current_project = obj.get_current_active_project()
        if current_project:
            url = reverse('admin:eeucontractors_project_change', args=[current_project.pk])
            return format_html('<a href="{}">{}</a>', url, current_project.name)
        return 'Available'
    current_project_link.short_description = 'Current Project'

    def total_projects_count(self, obj):
        return obj.total_projects_worked
    total_projects_count.short_description = 'Total Projects'

    def vendor_classification_display(self, obj):
        classification = obj.vendor_classification
        status_color = 'green' if classification['can_take_project'] else 'red'
        return format_html(
            '<strong>Type:</strong> {} (Level {})<br>'
            '<strong>Status:</strong> <span style="color: {};">{}</span><br>'
            '<strong>Projects:</strong> {} completed, {}% completion rate',
            classification['type'], classification['level'],
            status_color, classification['current_status'],
            classification['completed_projects'], classification['completion_rate']
        )
    vendor_classification_display.short_description = 'Vendor Classification'

# ============================================================================
# PROJECT MANAGEMENT ADMIN
# ============================================================================

class SubActivityInline(admin.TabularInline):
    model = SubActivity
    extra = 0

@admin.register(MainActivity)
class MainActivityAdmin(admin.ModelAdmin):
    list_display = ['main_activity', 'levels', 'subactivities_count']
    list_filter = ['main_activity', 'levels']
    inlines = [SubActivityInline]
    actions = ['reset_to_default_subactivities']

    def subactivities_count(self, obj):
        return obj.subactivities.count()
    subactivities_count.short_description = 'Sub-Activities'

    def reset_to_default_subactivities(self, request, queryset):
        updated = 0
        for activity in queryset:
            # Delete existing sub-activities
            activity.subactivities.all().delete()

            # Create default sub-activities
            main_activity_code = activity.main_activity
            default_subs = DEFAULT_SUBACTIVITIES.get(main_activity_code, [])

            if default_subs:
                sub_activities = []
                for sub in default_subs:
                    sub_activities.append(SubActivity(
                        main_activity=activity,
                        name=sub['name'],
                        order=sub['order'],
                        weight=sub['weight'],
                        base_duration=sub['base_duration']
                    ))

                SubActivity.objects.bulk_create(sub_activities)
                updated += 1

        messages.success(request, f"Reset sub-activities to defaults for {updated} main activities.")

    reset_to_default_subactivities.short_description = "Reset selected activities to default sub-activities"

@admin.register(SubActivity)
class SubActivityAdmin(admin.ModelAdmin):
    list_display = ['name', 'main_activity', 'order', 'weight', 'base_duration']
    list_filter = ['main_activity']
    search_fields = ['name']
    list_editable = ['weight', 'order', 'base_duration']
    ordering = ['main_activity', 'order']

@admin.register(Project)
class ProjectAdmin(admin.ModelAdmin):
    list_display = [
        'name', 'vendor', 'region', 'main_activity', 'status',
        'start_date', 'estimated_end_date', 'approved'
    ]
    list_filter = ['status', 'region', 'main_activity', 'approved', 'start_date']
    search_fields = ['name', 'vendor__name', 'vendor__identification_number']
    date_hierarchy = 'start_date'

    fieldsets = (
        ('Project Information', {
            'fields': ('name', 'region', 'main_activity', 'vendor', 'level', 'type')
        }),
        ('Timeline', {
            'fields': ('start_date', 'estimated_end_date', 'end_date', 'time_consumption')
        }),
        ('Status & Approval', {
            'fields': ('status', 'approved', 'approved_by', 'approved_date')
        }),
    )

# ============================================================================
# PROGRESS TRACKING ADMIN
# ============================================================================

class SubActivityWorkloadInline(admin.TabularInline):
    model = SubActivityWorkload
    extra = 0

@admin.register(ProjectProgress)
class ProjectProgressAdmin(admin.ModelAdmin):
    list_display = [
        'project', 'vendor', 'progress_percentage', 'status',
        'start_date', 'completion_date', 'last_updated'
    ]
    list_filter = ['status', 'start_date', 'completion_date']
    search_fields = ['project__name', 'vendor__name']
    readonly_fields = ['progress_percentage', 'last_updated']
    inlines = [SubActivityWorkloadInline]
    actions = ['check_missing_workloads', 'recalculate_progress']

    def check_missing_workloads(self, request, queryset):
        total_created = 0
        for progress in queryset:
            created = progress.create_missing_workloads()
            total_created += created
            if created > 0:
                progress.calculate_progress()

        if total_created > 0:
            messages.success(request, f"Created {total_created} missing workloads across {queryset.count()} projects.")
        else:
            messages.info(request, "No missing workloads found.")

    check_missing_workloads.short_description = "Check and create missing workloads"

    def recalculate_progress(self, request, queryset):
        for progress in queryset:
            progress.calculate_progress()
        messages.success(request, f"Recalculated progress for {queryset.count()} projects.")

    recalculate_progress.short_description = "Recalculate progress"

@admin.register(SubActivityWorkload)
class SubActivityWorkloadAdmin(admin.ModelAdmin):
    list_display = [
        'sub_activity', 'project_progress', 'quantity', 'completed_quantity',
        'unit_type', 'expected_duration'
    ]
    list_filter = ['unit_type', 'sub_activity__main_activity']
    search_fields = ['sub_activity__name', 'project_progress__project__name']

@admin.register(SubActivityProgress)
class SubActivityProgressAdmin(admin.ModelAdmin):
    list_display = ['workload', 'progress', 'start_date', 'end_date', 'actual_duration']
    list_filter = ['start_date', 'end_date']
    date_hierarchy = 'end_date'
    search_fields = ['workload__sub_activity__name']

# ============================================================================
# PERFORMANCE & HISTORY ADMIN
# ============================================================================

@admin.register(ProjectVendorsHistory)
class ProjectVendorsHistoryAdmin(admin.ModelAdmin):
    list_display = [
        'vendor', 'project', 'status', 'start_date', 'end_date',
        'cause_of_delay_or_suspension'
    ]
    list_filter = ['status', 'cause_of_delay_or_suspension', 'start_date']
    search_fields = ['vendor__name', 'project__name']
    date_hierarchy = 'start_date'

@admin.register(VendorDocument)
class VendorDocumentAdmin(admin.ModelAdmin):
    list_display = ['vendor', 'document_type', 'description', 'expiry_date', 'uploaded_at', 'is_expired']
    list_filter = ['document_type', 'expiry_date', 'uploaded_at']
    search_fields = ['vendor__name', 'description']

    def is_expired(self, obj):
        if obj.expiry_date < timezone.now().date():
            return format_html('<span style="color: red;">Expired</span>')
        return format_html('<span style="color: green;">Valid</span>')
    is_expired.short_description = 'Status'

@admin.register(RegionalPerformance)
class RegionalPerformanceAdmin(admin.ModelAdmin):
    list_display = [
        'region', 'total_projects', 'completed_projects', 'completion_rate',
        'total_vendors', 'active_vendors', 'vendor_utilization_rate', 'last_updated'
    ]
    readonly_fields = [
        'total_projects', 'completed_projects', 'active_projects', 'delayed_projects',
        'total_vendors', 'active_vendors', 'blacklisted_vendors',
        'average_project_completion_time', 'average_progress_percentage',
        'completion_rate', 'vendor_utilization_rate', 'last_updated'
    ]
    actions = ['update_statistics']

    def update_statistics(self, request, queryset):
        for performance in queryset:
            performance.update_statistics()
        messages.success(request, f"Updated statistics for {queryset.count()} regions.")

    update_statistics.short_description = "Update regional statistics"

@admin.register(RegionalProjectTemplate)
class RegionalProjectTemplateAdmin(admin.ModelAdmin):
    list_display = [
        'region', 'main_activity', 'status', 'assigned_vendor',
        'approved_by_director', 'director_approval_date'
    ]
    list_filter = ['main_activity', 'is_available', 'approved_by_director', 'region']
    search_fields = ['region__name', 'assigned_vendor__name']

# ============================================================================
# ADMIN SITE CUSTOMIZATION
# ============================================================================

# Tab title (shown in browser tab)
admin.site.site_title = "EEU Admin Portal"

# Top-left header
admin.site.site_header = "EEU Contractors Management System"

# Main dashboard page title
admin.site.index_title = "Project Progress Management System"
