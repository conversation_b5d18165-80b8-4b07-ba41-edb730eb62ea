{% extends 'base.html' %}
{% load static %}

{% block content %}
<div class="container mt-4">
    <h2>Workload Details</h2>
    
    <div class="card mb-4">
        <div class="card-header">
            <h4>Workload Information</h4>
        </div>
        <div class="card-body">
            <div class="row">
                <div class="col-md-6">
                    <table class="table table-bordered">
                        <tr>
                            <th>Project:</th>
                            <td>{{ workload.project_progress.project.name }}</td>
                        </tr>
                        <tr>
                            <th>Vendor:</th>
                            <td>{{ workload.project_progress.vendor.name }}</td>
                        </tr>
                        <tr>
                            <th>Main Activity:</th>
                            <td>
                                {% if workload.project_progress.main_activity %}
                                    {{ workload.project_progress.main_activity.get_main_activity_display }}
                                {% else %}
                                    <span class="text-muted">Not assigned</span>
                                {% endif %}
                            </td>
                        </tr>
                        <tr>
                            <th>Sub Activity:</th>
                            <td>{{ workload.sub_activity.name }}</td>
                        </tr>
                    </table>
                </div>
                <div class="col-md-6">
                    <table class="table table-bordered">
                        <tr>
                            <th>Quantity:</th>
                            <td>{{ workload.quantity }} {{ workload.unit_type|default:"units" }}</td>
                        </tr>
                        <tr>
                            <th>Completed Quantity:</th>
                            <td>
                                <form method="post" action="{% url 'workload_detail' workload_id=workload.id %}" class="d-inline">
                                    {% csrf_token %}
                                    <div class="input-group input-group-sm" style="max-width: 200px;">
                                        <input type="number"
                                               name="completed_quantity"
                                               value="{{ workload.completed_quantity|default:0 }}"
                                               class="form-control"
                                               min="0"
                                               max="{{ workload.quantity }}"
                                               step="1">
                                        <span class="input-group-text">{{ workload.unit_type|default:"units" }}</span>
                                        <button type="submit" class="btn btn-success btn-sm">
                                            <i class="bi bi-check"></i>
                                        </button>
                                    </div>
                                </form>
                            </td>
                        </tr>
                        <tr>
                            <th>Unit Type:</th>
                            <td>{{ workload.unit_type|default:"Not specified" }}</td>
                        </tr>
                        <tr>
                            <th>Expected Duration:</th>
                            <td>{{ workload.expected_duration|default:"Not specified" }} days</td>
                        </tr>
                    </table>
                </div>
            </div>
            <div class="row mt-3">
                <div class="col-12">
                    <table class="table table-bordered">
                        <tr>
                            <th style="width: 20%;">Quantity Progress:</th>
                            <td>
                                <div class="progress" style="height: 25px;">
                                    <div class="progress-bar bg-success"
                                         role="progressbar"
                                         style="width: {{ workload.quantity_progress_percentage|default:0 }}%;"
                                         data-progress="{{ workload.quantity_progress_percentage|default:0 }}">
                                        {{ workload.quantity_progress_percentage|default:0 }}%
                                    </div>
                                </div>
                                <small class="text-muted mt-1 d-block">
                                    {{ workload.completed_quantity }}/{{ workload.quantity }} {{ workload.unit_type }} completed
                                </small>
                            </td>
                        </tr>
                        <tr>
                            <th style="width: 20%;">Current Progress:</th>
                            <td>
                                <div class="progress" style="height: 25px;">
                                    <div class="progress-bar bg-primary"
                                         role="progressbar"
                                         style="width: {{ workload.current_progress }}%;"
                                         data-progress="{{ workload.current_progress }}">
                                        {{ workload.current_progress }}%
                                    </div>
                                </div>
                                <small class="text-muted mt-1 d-block">
                                    <i class="fas fa-info-circle"></i>
                                    {% if workload.progress_type == 'assessment' %}
                                        <span class="badge bg-info">Assessment-based</span>
                                        {% if workload.quantity_progress_percentage != workload.current_progress %}
                                            - Validated from manual assessment
                                            <br><strong>Details:</strong> {{ workload.progress_details }}
                                        {% else %}
                                            - Matches quantity progress
                                        {% endif %}
                                    {% else %}
                                        <span class="badge bg-secondary">Quantity-based</span>
                                        - No manual assessments available
                                    {% endif %}
                                </small>
                            </td>
                        </tr>
                    </table>
                </div>
            </div>
        </div>
    </div>
    
    <div class="card mb-4">
        <div class="card-header d-flex justify-content-between align-items-center">
            <h4>Progress Records</h4>
        </div>
        <div class="card-body">
            {% if progress_records %}
                <table class="table table-striped">
                    <thead>
                        <tr>
                            <th>Progress %</th>
                            <th>Start Date</th>
                            <th>End Date</th>
                            <th>Duration</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for record in progress_records %}
                        <tr>
                            <td>{{ record.progress }}%</td>
                            <td>{{ record.start_date }}</td>
                            <td>{{ record.end_date }}</td>
                            <td>{{ record.actual_duration }} days</td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            {% else %}
                <p class="text-muted">No progress records found.</p>
            {% endif %}
        </div>
    </div>
    
    <div class="card">
        <div class="card-header">
            <h4>Add Progress Record</h4>
        </div>
        <div class="card-body">
            <!-- Debug info -->
            {% if formset.errors %}
            <div class="alert alert-danger">
                <p>Form errors:</p>
                {{ formset.errors }}
            </div>
            {% endif %}
            
            <form method="post">
                {% csrf_token %}
                {{ formset.management_form }}
                
                <table class="table">
                    <thead>
                        <tr>
                            <th>Progress %</th>
                            <th>Start Date</th>
                            <th>End Date</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for form in formset %}
                        <tr>
                            <td>
                                {{ form.id }}
                                {{ form.progress }}
                                {% if form.progress.errors %}
                                <div class="text-danger">{{ form.progress.errors }}</div>
                                {% endif %}
                            </td>
                            <td>
                                {{ form.start_date }}
                                {% if form.start_date.errors %}
                                <div class="text-danger">{{ form.start_date.errors }}</div>
                                {% endif %}
                            </td>
                            <td>
                                {{ form.end_date }}
                                {% if form.end_date.errors %}
                                <div class="text-danger">{{ form.end_date.errors }}</div>
                                {% endif %}
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
                
                <div class="mt-3">
                    <button type="submit" class="btn btn-primary">Save Progress</button>
                    <a href="{% url 'workload_list' progress_id=workload.project_progress.id %}" class="btn btn-secondary">Back to Workloads</a>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
// Set progress bar width dynamically to avoid Django template syntax in CSS
document.addEventListener('DOMContentLoaded', function() {
  const progressBars = document.querySelectorAll('.progress-bar[data-progress]');
  progressBars.forEach(function(bar) {
    const progress = bar.getAttribute('data-progress');
    bar.style.width = progress + '%';
  });
});
</script>

{% endblock %}




