{% extends 'base.html' %}
{% load static %}

{% block content %}
<div class="container mt-4">
    <h2>Workload Details</h2>
    
    <div class="card mb-4">
        <div class="card-header">
            <h4>Workload Information</h4>
        </div>
        <div class="card-body">
            <div class="row">
                <div class="col-md-6">
                    <table class="table table-bordered">
                        <tr>
                            <th>Project:</th>
                            <td>{{ workload.project_progress.project.name }}</td>
                        </tr>
                        <tr>
                            <th>Vendor:</th>
                            <td>{{ workload.project_progress.vendor.name }}</td>
                        </tr>
                        <tr>
                            <th>Main Activity:</th>
                            <td>
                                {% if workload.project_progress.main_activity %}
                                    {{ workload.project_progress.main_activity.get_main_activity_display }}
                                {% else %}
                                    <span class="text-muted">Not assigned</span>
                                {% endif %}
                            </td>
                        </tr>
                        <tr>
                            <th>Sub Activity:</th>
                            <td>{{ workload.sub_activity.name }}</td>
                        </tr>
                    </table>
                </div>
                <div class="col-md-6">
                    <table class="table table-bordered">
                        <tr>
                            <th>Quantity:</th>
                            <td>{{ workload.quantity }} {{ workload.unit_type|default:"units" }}</td>
                        </tr>
                        <tr>
                            <th>Completed Quantity:</th>
                            <td>
                                <form method="post" action="{% url 'workload_detail' workload_id=workload.id %}" class="d-inline">
                                    {% csrf_token %}
                                    <div class="input-group input-group-sm" style="max-width: 200px;">
                                        <input type="number"
                                               name="completed_quantity"
                                               value="{{ workload.completed_quantity|default:0 }}"
                                               class="form-control"
                                               min="0"
                                               max="{{ workload.quantity }}"
                                               step="1">
                                        <span class="input-group-text">{{ workload.unit_type|default:"units" }}</span>
                                        <button type="submit" class="btn btn-success btn-sm">
                                            <i class="bi bi-check"></i>
                                        </button>
                                    </div>
                                </form>
                            </td>
                        </tr>
                        <tr>
                            <th>Unit Type:</th>
                            <td>{{ workload.unit_type|default:"Not specified" }}</td>
                        </tr>
                        <tr>
                            <th>Expected Duration:</th>
                            <td>{{ workload.expected_duration|default:"Not specified" }} days</td>
                        </tr>
                    </table>
                </div>
            </div>
            <div class="row mt-3">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header bg-light">
                            <h5 class="mb-0">Progress Overview</h5>
                        </div>
                        <div class="card-body">
                            <div class="row g-3">
                                <!-- Quantity Progress -->
                                <div class="col-md-6">
                                    <div class="d-flex align-items-center justify-content-between p-3 border rounded bg-light">
                                        <div>
                                            <strong class="text-success">Quantity Progress</strong>
                                            <div class="mt-1">
                                                <span class="badge bg-success fs-6">{{ workload.quantity_progress_percentage|default:0 }}%</span>
                                                <small class="text-muted ms-2">
                                                    ({{ workload.completed_quantity }}/{{ workload.quantity }} {{ workload.unit_type }})
                                                </small>
                                            </div>
                                        </div>
                                        <i class="bi bi-calculator-fill text-success fs-3"></i>
                                    </div>
                                </div>

                                <!-- Current Progress -->
                                <div class="col-md-6">
                                    <div class="d-flex align-items-center justify-content-between p-3 border rounded bg-light">
                                        <div>
                                            <strong class="text-primary">Current Progress</strong>
                                            <div class="mt-1">
                                                <span class="badge bg-primary fs-6">{{ workload.current_progress }}%</span>
                                                <small class="text-muted ms-2">
                                                    {% if workload.progress_type == 'assessment' %}
                                                        <i class="bi bi-person-check-fill"></i> Assessment-based
                                                    {% else %}
                                                        <i class="bi bi-calculator"></i> Quantity-based
                                                    {% endif %}
                                                </small>
                                            </div>
                                        </div>
                                        {% if workload.progress_type == 'assessment' %}
                                            <i class="bi bi-person-check-fill text-primary fs-3"></i>
                                        {% else %}
                                            <i class="bi bi-calculator text-primary fs-3"></i>
                                        {% endif %}
                                    </div>
                                </div>
                            </div>

                            <!-- Validation Message (only show if different and meaningful) -->
                            {% if workload.progress_type == 'assessment' and workload.quantity_progress_percentage != workload.current_progress %}
                                {% if workload.latest_progress and workload.latest_progress.progress != workload.current_progress %}
                                    <div class="mt-3 p-2 bg-info bg-opacity-10 border border-info rounded">
                                        <small class="text-info">
                                            <i class="bi bi-info-circle-fill"></i>
                                            Manual assessment validated: {{ workload.latest_progress.progress }}% → {{ workload.current_progress }}%
                                        </small>
                                    </div>
                                {% endif %}
                            {% endif %}
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="card mb-4">
        <div class="card-header d-flex justify-content-between align-items-center">
            <h4>Progress Records</h4>
        </div>
        <div class="card-body">
            {% if progress_records %}
                <table class="table table-striped">
                    <thead>
                        <tr>
                            <th>Progress %</th>
                            <th>Start Date</th>
                            <th>End Date</th>
                            <th>Duration</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for record in progress_records %}
                        <tr>
                            <td>{{ record.progress }}%</td>
                            <td>{{ record.start_date }}</td>
                            <td>{{ record.end_date }}</td>
                            <td>{{ record.actual_duration }} days</td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            {% else %}
                <p class="text-muted">No progress records found.</p>
            {% endif %}
        </div>
    </div>
    
    <div class="card">
        <div class="card-header bg-primary text-white">
            <h5 class="mb-0">
                <i class="bi bi-plus-circle me-2"></i>Add Progress Record
            </h5>
        </div>
        <div class="card-body">
            {% if formset.errors %}
                <div class="alert alert-danger">
                    <i class="bi bi-exclamation-triangle-fill me-2"></i>
                    <strong>Please correct the following errors:</strong>
                    {{ formset.errors }}
                </div>
            {% endif %}

            <form method="post">
                {% csrf_token %}
                {{ formset.management_form }}

                <div class="row g-3">
                    {% for form in formset %}
                        <div class="col-md-4">
                            <label class="form-label fw-bold">Progress %</label>
                            {{ form.id }}
                            <div class="input-group">
                                {{ form.progress }}
                                <span class="input-group-text">%</span>
                            </div>
                            {% if form.progress.errors %}
                                <div class="text-danger small mt-1">{{ form.progress.errors }}</div>
                            {% endif %}
                        </div>

                        <div class="col-md-4">
                            <label class="form-label fw-bold">Start Date</label>
                            {{ form.start_date }}
                            {% if form.start_date.errors %}
                                <div class="text-danger small mt-1">{{ form.start_date.errors }}</div>
                            {% endif %}
                        </div>

                        <div class="col-md-4">
                            <label class="form-label fw-bold">End Date</label>
                            {{ form.end_date }}
                            {% if form.end_date.errors %}
                                <div class="text-danger small mt-1">{{ form.end_date.errors }}</div>
                            {% endif %}
                        </div>
                    {% endfor %}
                </div>

                <div class="mt-4 d-flex gap-2">
                    <button type="submit" class="btn btn-primary">
                        <i class="bi bi-check-circle me-2"></i>Save Progress
                    </button>
                    <a href="{% url 'workload_list' progress_id=workload.project_progress.id %}" class="btn btn-secondary">
                        <i class="bi bi-arrow-left me-2"></i>Back to Workloads
                    </a>
                </div>
            </form>
        </div>
    </div>
</div>

<style>
/* Custom styles to ensure proper layout */
.progress-card {
    min-height: 120px;
}

.progress-card .card-body {
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.progress-info {
    flex: 1;
}

.progress-icon {
    font-size: 2.5rem;
    opacity: 0.7;
}

/* Ensure responsive layout works */
@media (max-width: 768px) {
    .progress-card .card-body {
        flex-direction: column;
        text-align: center;
    }

    .progress-icon {
        margin-top: 10px;
    }
}

/* Form styling */
.form-control, .form-select {
    border-radius: 0.375rem;
    border: 1px solid #ced4da;
}

.form-control:focus, .form-select:focus {
    border-color: #86b7fe;
    box-shadow: 0 0 0 0.25rem rgba(13, 110, 253, 0.25);
}
</style>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Ensure form inputs have proper classes
    const inputs = document.querySelectorAll('input[type="number"], input[type="date"]');
    inputs.forEach(function(input) {
        if (!input.classList.contains('form-control')) {
            input.classList.add('form-control');
        }
    });

    // Set progress bar widths if any exist
    const progressBars = document.querySelectorAll('.progress-bar[data-progress]');
    progressBars.forEach(function(bar) {
        const progress = bar.getAttribute('data-progress');
        bar.style.width = progress + '%';
    });
});
</script>

{% endblock %}




