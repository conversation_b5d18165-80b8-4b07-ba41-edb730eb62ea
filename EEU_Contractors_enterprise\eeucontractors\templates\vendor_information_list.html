{% extends 'base.html' %} 
{% block content %}
<div class="container mt-0">
  <div class="card shadow-sm border-0 rounded-3 mb-4">
    <div class="card-header  py-3 text-center fw-bold" style="">
      <h2 class="mb-0">Registered Vendors</h2>
    </div>
    <div class="card-body p-0">
      <div class="table-responsive">
        <table class="table table-bordered table-hover mb-0">
          <thead>
            <tr>
              <th class="text-nowrap">Name</th>
              <th class="text-nowrap">Phone</th>
              <th class="text-nowrap">Type</th>
              <th class="text-nowrap">Level</th>
              <th class="text-nowrap">Id</th>
              <th class="text-nowrap">Registered Date</th>
              <th class="text-nowrap">Experience</th>
              <th class="text-nowrap">Status</th>
            </tr>
          </thead>
          <tbody>
            {% for vendor in vendors %}
            <tr>
              <td class="fw-bold">{{ vendor.name }}</td>
              <td>{{ vendor.phone_number }}</td>
              <td>{{ vendor.type }}</td>
              <td>{{ vendor.level }}</td>
              <td>{{ vendor.identification_number }}</td>
              <td>{{ vendor.vendor_registration.registration_date|date:"Y-m-d" }}</td>
              <td>{{ vendor.experience }} yrs</td>
              <td>
                <span class="badge rounded-pill 
                  {% if vendor.status == 'active' %}
                    bg-success
                  {% else %}
                    bg-warning text-dark
                  {% endif %}">
                  {{ vendor.status }}
                </span>
              </td>
            </tr>
            {% empty %}
            <tr>
              <td colspan="8" class="text-center py-4 text-muted">No vendors found</td>
            </tr>
            {% endfor %}
          </tbody>
        </table>
      </div>
    </div>
  </div>
</div>

<style>

 body {
  background-color: #faf7f0; /* soft warm beige, easy on eyes, no green/orange overload */
}

.card-header {
  
  border-top: 3px solid #f68b1e;    /* solid orange top border */
  border-bottom: 3px solid #28a745; /* solid green bottom border */
  padding: 0.5rem 1rem;
  font-family: "Times New Roman", Times, serif;
  height: 50px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 0; /* remove rounding */
}

.card-header h2 {
  margin: 0;
  font-weight: 700;
  font-size: 1.5rem;
}

  
</style>
{% endblock %}



