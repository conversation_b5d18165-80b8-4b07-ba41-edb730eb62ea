{% comment %}
Centralized message handling component
Usage: {% include 'components/messages.html' %}
{% endcomment %}

{% if messages %}
  <div class="messages mb-3">
    {% for message in messages %}
      <div class="alert alert-{{ message.tags }} alert-dismissible fade show mb-2 auto-dismiss" 
           role="alert" 
           data-bs-autohide="true" 
           data-bs-delay="5000">
        <div class="d-flex align-items-center">
          {% if message.tags == 'success' %}
            <i class="bi bi-check-circle-fill me-2 text-success"></i>
          {% elif message.tags == 'error' %}
            <i class="bi bi-exclamation-triangle-fill me-2 text-danger"></i>
          {% elif message.tags == 'warning' %}
            <i class="bi bi-exclamation-circle-fill me-2 text-warning"></i>
          {% else %}
            <i class="bi bi-info-circle-fill me-2 text-info"></i>
          {% endif %}
          <span>{{ message }}</span>
        </div>
        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
      </div>
    {% endfor %}
  </div>
  
  <!-- Auto-dismiss script -->
  <script>
    document.addEventListener('DOMContentLoaded', function() {
      // Auto-dismiss success messages after 4 seconds
      const successAlerts = document.querySelectorAll('.alert-success');
      successAlerts.forEach(function(alert) {
        setTimeout(function() {
          if (alert && !alert.classList.contains('d-none')) {
            const bsAlert = new bootstrap.Alert(alert);
            bsAlert.close();
          }
        }, 4000);
      });
      
      // Auto-dismiss info messages after 3 seconds
      const infoAlerts = document.querySelectorAll('.alert-info');
      infoAlerts.forEach(function(alert) {
        setTimeout(function() {
          if (alert && !alert.classList.contains('d-none')) {
            const bsAlert = new bootstrap.Alert(alert);
            bsAlert.close();
          }
        }, 3000);
      });
    });
  </script>
{% endif %}
