{% extends 'base.html' %}
{% load static %}

{% block content %}
<div class="container mt-4">
  <div class="row justify-content-center">
    <div class="col-lg-8 col-md-10">
      <div class="card">
        <div class="card-header">
          <h3 class="mb-0">{{ title }}</h3>
        </div>
        <div class="card-body p-4">
          {% include 'components/messages.html' %}

          <form method="post" class="needs-validation" novalidate>
            {% csrf_token %}

            <div class="row">
              {% for field in form %}
                {% if field.is_hidden %}
                  {{ field }}
                {% else %}
                  <div class="col-md-6 mb-4">
                    <label for="{{ field.id_for_label }}" class="form-label fw-bold">
                      {{ field.label }}
                      {% if field.field.required %}<span class="text-danger">*</span>{% endif %}
                    </label>
                    {{ field }}
                    {% if field.help_text %}
                      <div class="form-text text-muted">
                        <i class="bi bi-info-circle me-1"></i>{{ field.help_text }}
                      </div>
                    {% endif %}
                    {% if field.errors %}
                      <div class="invalid-feedback d-block">
                        {% for error in field.errors %}
                          <i class="bi bi-exclamation-triangle me-1"></i>{{ error }}
                        {% endfor %}
                      </div>
                    {% endif %}
                  </div>
                {% endif %}
              {% endfor %}
            </div>
        
            <div class="col-12">
              <div class="d-flex justify-content-between align-items-center mt-4 pt-3 border-top">
                <div>
                  {% if workload %}
                    <a href="{% url 'workload_detail' workload_id=workload.id %}" class="btn btn-outline-secondary btn-lg">
                      <i class="bi bi-arrow-left me-2"></i>Cancel
                    </a>
                  {% else %}
                    <a href="{% url 'workload_list' progress_id=project_progress.id %}" class="btn btn-outline-secondary btn-lg">
                      <i class="bi bi-arrow-left me-2"></i>Cancel
                    </a>
                  {% endif %}
                </div>
                <div>
                  <button type="submit" class="btn btn-success btn-lg px-4">
                    <i class="bi bi-check-circle me-2"></i>Save Workload
                  </button>
                </div>
              </div>
            </div>
          </form>
        </div>
      </div>
    </div>
  </div>
</div>

<script>
// Auto-calculate expected duration based on selected sub-activity and quantity
function updateExpectedDuration() {
    const subActivitySelect = document.getElementById('id_sub_activity');
    const quantityField = document.getElementById('id_quantity');
    const durationField = document.getElementById('id_expected_duration');

    if (subActivitySelect && quantityField && durationField) {
        const subActivityId = subActivitySelect.value;
        const quantity = parseInt(quantityField.value) || 1;

        // Sub-activity base durations (from defaults.py)
        const baseDurations = {
            {% for sub_activity in form.sub_activity.field.queryset %}
                {{ sub_activity.id }}: {{ sub_activity.base_duration|default:1 }},
            {% endfor %}
        };

        if (baseDurations[subActivityId] && quantity > 0) {
            const baseDuration = baseDurations[subActivityId];
            const calculatedDuration = Math.max(1, Math.floor(baseDuration * quantity));
            durationField.value = calculatedDuration;

            // Show calculation formula
            const formulaText = `${baseDuration} × ${quantity} = ${calculatedDuration} days`;
            let formulaDisplay = document.getElementById('duration-formula');
            if (!formulaDisplay) {
                formulaDisplay = document.createElement('small');
                formulaDisplay.id = 'duration-formula';
                formulaDisplay.className = 'text-muted d-block mt-1';
                durationField.parentNode.appendChild(formulaDisplay);
            }
            formulaDisplay.textContent = formulaText;
        }
    }
}

// Add event listeners
document.addEventListener('DOMContentLoaded', function() {
    const subActivitySelect = document.getElementById('id_sub_activity');
    const quantityField = document.getElementById('id_quantity');

    if (subActivitySelect) {
        subActivitySelect.addEventListener('change', updateExpectedDuration);
    }
    if (quantityField) {
        quantityField.addEventListener('input', updateExpectedDuration);
    }

    // Calculate on page load
    updateExpectedDuration();
});

// Auto-fill unit type based on sub-activity
document.addEventListener('DOMContentLoaded', function() {
    const subActivitySelect = document.getElementById('id_sub_activity');
    const unitTypeField = document.getElementById('id_unit_type');

    if (subActivitySelect && unitTypeField) {
        subActivitySelect.addEventListener('change', function() {
            const selectedText = this.options[this.selectedIndex].text.toLowerCase();

            // Auto-suggest unit types based on sub-activity name
            if (selectedText.includes('pole') || selectedText.includes('erecting')) {
                unitTypeField.value = 'poles';
            } else if (selectedText.includes('meter') || selectedText.includes('cable') || selectedText.includes('wiring')) {
                unitTypeField.value = 'meters';
            } else if (selectedText.includes('transformer')) {
                unitTypeField.value = 'transformers';
            } else if (selectedText.includes('foundation') || selectedText.includes('structure')) {
                unitTypeField.value = 'structures';
            } else if (selectedText.includes('building') || selectedText.includes('construction')) {
                unitTypeField.value = 'buildings';
            } else {
                unitTypeField.value = 'units';
            }
        });
    }
});
</script>
{% endblock %}