{% extends 'base.html' %}
{% load static %}

{% block title %}Confirm Workload Deletion{% endblock %}

{% block content %}
<div class="container mt-4">
  <div class="card shadow">
    <div class="card-header bg-danger text-white">
      <h3><i class="bi bi-exclamation-triangle-fill me-2"></i>Confirm Workload Deletion</h3>
    </div>
    <div class="card-body">
      <div class="alert alert-warning">
        <p class="mb-0"><strong>Warning:</strong> This action cannot be undone. All progress records associated with this workload will also be deleted.</p>
      </div>
      
      <div class="card mb-4">
        <div class="card-header bg-light">
          <h5>Workload Information</h5>
        </div>
        <div class="card-body">
          <table class="table">
            <tr>
              <th style="width: 30%">Project:</th>
              <td>{{ workload.project_progress.project.name }}</td>
            </tr>
            <tr>
              <th>Sub Activity:</th>
              <td>{{ workload.sub_activity.name }}</td>
            </tr>
            <tr>
              <th>Quantity:</th>
              <td>{{ workload.quantity }} units</td>
            </tr>
            <tr>
              <th>Expected Duration:</th>
              <td>{{ workload.expected_duration }} days</td>
            </tr>
          </table>
        </div>
      </div>
      
      <p class="lead text-center mb-4">Are you sure you want to delete this workload?</p>
      
      <form method="post" class="d-flex justify-content-center gap-3">
        {% csrf_token %}
        <button type="submit" class="btn btn-danger">
          <i class="bi bi-trash me-1"></i> Confirm Delete
        </button>
        <a href="{% url 'workload_list' progress_id=workload.project_progress.id %}" class="btn btn-secondary">
          <i class="bi bi-x-circle me-1"></i> Cancel
        </a>
      </form>
    </div>
  </div>
</div>
{% endblock %}
