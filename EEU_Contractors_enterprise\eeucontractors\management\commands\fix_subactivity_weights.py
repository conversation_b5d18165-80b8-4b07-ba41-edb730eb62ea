from django.core.management.base import BaseCommand
from eeucontractors.models import MainActivity, SubActivity
from django.db.models import Sum


class Command(BaseCommand):
    help = 'Fix SubActivity weights to ensure they add up to 100% for each MainActivity'

    def add_arguments(self, parser):
        parser.add_argument(
            '--dry-run',
            action='store_true',
            help='Show what would be changed without making actual changes',
        )

    def handle(self, *args, **options):
        dry_run = options['dry_run']
        
        if dry_run:
            self.stdout.write(self.style.WARNING('DRY RUN MODE - No changes will be made'))
        
        main_activities = MainActivity.objects.all()
        
        for main_activity in main_activities:
            self.stdout.write(f"\n📋 Processing: {main_activity.name}")
            
            sub_activities = SubActivity.objects.filter(main_activity=main_activity).order_by('order')
            total_subs = sub_activities.count()
            
            if total_subs == 0:
                self.stdout.write(self.style.WARNING(f"  ⚠️  No sub-activities found"))
                continue
            
            # Calculate current total weight
            current_total = sub_activities.aggregate(Sum('weight'))['weight__sum'] or 0
            
            self.stdout.write(f"  📊 Current total weight: {current_total}%")
            self.stdout.write(f"  📊 Number of sub-activities: {total_subs}")
            
            if current_total == 100:
                self.stdout.write(self.style.SUCCESS(f"  ✅ Weights already balanced"))
                continue
            
            # Calculate equal weight distribution
            equal_weight = round(100.0 / total_subs, 2)
            remainder = 100.0 - (equal_weight * total_subs)
            
            self.stdout.write(f"  🔧 Redistributing weights equally: {equal_weight}% each")
            
            if not dry_run:
                # Update weights
                for i, sub_activity in enumerate(sub_activities):
                    new_weight = equal_weight
                    # Add remainder to the last sub-activity
                    if i == total_subs - 1:
                        new_weight += remainder
                    
                    old_weight = sub_activity.weight
                    sub_activity.weight = new_weight
                    sub_activity.save()
                    
                    self.stdout.write(f"    📝 {sub_activity.name}: {old_weight}% → {new_weight}%")
                
                # Verify the total
                new_total = sub_activities.aggregate(Sum('weight'))['weight__sum'] or 0
                self.stdout.write(self.style.SUCCESS(f"  ✅ New total weight: {new_total}%"))
            else:
                # Show what would be changed
                for i, sub_activity in enumerate(sub_activities):
                    new_weight = equal_weight
                    if i == total_subs - 1:
                        new_weight += remainder
                    
                    self.stdout.write(f"    📝 {sub_activity.name}: {sub_activity.weight}% → {new_weight}%")
        
        if dry_run:
            self.stdout.write(self.style.WARNING('\n🔍 This was a dry run. Use --dry-run=false to apply changes.'))
        else:
            self.stdout.write(self.style.SUCCESS('\n✅ Weight redistribution completed!'))
