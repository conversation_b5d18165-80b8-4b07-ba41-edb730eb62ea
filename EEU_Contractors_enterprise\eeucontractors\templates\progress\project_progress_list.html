{% extends 'base.html' %} {% block content %}
<div class="container mt-4">
  <div class="d-flex justify-content-between align-items-center mb-3">
    <h2 class="text-primary">Project Progress List</h2>
    <a href="{% url 'create_project_progress' %}" class="btn btn-success"
      >Add New Progress</a
    >
  </div>
  <table class="table table-striped table-hover shadow-sm">
    <thead class="table-dark">
      <tr>
        <th>Project</th>
        <th>Vendor</th>
        <th>Main Activity</th>
        <th>Progress %</th>
        <th>Status</th>
        <th>Start Date</th>
        <th>Completion Date</th>
        <th>Action</th>
      </tr>
    </thead>
    <tbody>
      {% for progress in progresses %}
      <tr>
        <td>{{ progress.project.name }}</td>
        <td>{{ progress.vendor.name }}</td>
        <td>{{ progress.main_activity.main_activity }}</td>
        <td>{{ progress.progress_percentage }}%</td>
        <td>{{ progress.get_status_display }}</td>
        <td>{{ progress.start_date }}</td>
        <td>{{ progress.completion_date|default:"Not completed" }}</td>
        {% comment %} <td>
          <a
            href="{% url 'project_progress_update' progress.pk %}"
            class="btn btn-sm btn-info"
            >Edit</a
          > {% endcomment %}
           <td>
        <!-- This is where you paste the link -->
        <a href="{% url 'select_project_progress' progress_id=progress.id %}" class="text-decoration-underline" style="color: orange; font-weight: bold;">
          View Workloads
        </a>
      </td>
        
      </tr>
      {% empty %}
      <tr>
        <td colspan="7" class="text-center">No progress data available.</td>
      </tr>
      {% endfor %}
    </tbody>
  </table>
</div>
{% endblock %} {% comment %} {% extends 'base.html' %} {% block content %}
<div class="container mt-4">
  <h2>Project Progress List</h2>

  <table class="table table-bordered table-striped">
    <thead>
      <tr>
        <th>Project</th>
        <th>Vendor</th>
        <th>Main Activity</th>
        <th>Sub Activity</th>
        <th>Start Date</th>
        <th>Progress %</th>
        <th>Status</th>
        <th>Actions</th>
      </tr>
    </thead>
    <tbody>
      {% for progress in page_obj %}
      <tr>
        <td>{{ progress.project }}</td>
        <td>{{ progress.vendor }}</td>
        <td>{{ progress.main_activity.main_activity }}</td>
        <td>{{ progress.sub_activity }}</td>
        <td>{{ progress.start_date }}</td>
        <td>{{ progress.progress_percentage }}%</td>
        <td>{{ progress.get_status_display }}</td>
        <td>
          <button
            class="btn btn-sm btn-warning edit-progress-btn"
            data-url="{% url 'edit_project_progress' progress.pk %}"
          >
            Edit
          </button>
        </td>
      </tr>
      {% empty %}
      <tr>
        <td colspan="8">No project progress found.</td>
      </tr>
      {% endfor %}
    </tbody>
  </table>

  <nav aria-label="Page navigation">
    <ul class="pagination">
      {% if page_obj.has_previous %}
      <li class="page-item">
        <a
          class="page-link"
          href="?page={{ page_obj.previous_page_number }}"
          aria-label="Previous"
        >
          <span aria-hidden="true">&laquo;</span>
        </a>
      </li>
      {% else %}
      <li class="page-item disabled">
        <span class="page-link">&laquo;</span>
      </li>
      {% endif %} {% for num in page_obj.paginator.page_range %}
      <li class="page-item {% if page_obj.number == num %}active{% endif %}">
        <a class="page-link" href="?page={{ num }}">{{ num }}</a>
      </li>
      {% endfor %} {% if page_obj.has_next %}
      <li class="page-item">
        <a
          class="page-link"
          href="?page={{ page_obj.next_page_number }}"
          aria-label="Next"
        >
          <span aria-hidden="true">&raquo;</span>
        </a>
      </li>
      {% else %}
      <li class="page-item disabled">
        <span class="page-link">&raquo;</span>
      </li>
      {% endif %}
    </ul>
  </nav>
</div>

<!-- Modal -->
<div
  class="modal fade"
  id="modal-progress"
  tabindex="-1"
  aria-labelledby="modal-progress-label"
  aria-hidden="true"
>
  <div class="modal-dialog modal-lg">
    <div class="modal-content" id="modal-progress-content">
      <!-- AJAX loaded form goes here -->
    </div>
  </div>
</div>

{% endblock %} {% block scripts %}
<script>
  document.addEventListener("DOMContentLoaded", function () {
    const modal = new bootstrap.Modal(
      document.getElementById("modal-progress")
    );

    document.querySelectorAll(".edit-progress-btn").forEach((btn) => {
      btn.addEventListener("click", function () {
        const url = this.dataset.url;
        fetch(url)
          .then((response) => response.json())
          .then((data) => {
            document.getElementById("modal-progress-content").innerHTML =
              data.html_form;
            modal.show();
            bindFormSubmit();
          });
      });
    });

    function bindFormSubmit() {
      const form = document.querySelector("#modal-progress-content form");
      if (!form) return;
      form.addEventListener("submit", function (e) {
        e.preventDefault();
        const url = form.action;
        const formData = new FormData(form);
        fetch(url, {
          method: "POST",
          headers: {
            "X-Requested-With": "XMLHttpRequest",
            "X-CSRFToken": form.querySelector("[name=csrfmiddlewaretoken]")
              .value,
          },
          body: formData,
        })
          .then((response) => response.json())
          .then((data) => {
            if (data.success) {
              modal.hide();
              location.reload(); // Refresh page to show updated data
            } else {
              document.getElementById("modal-progress-content").innerHTML =
                data.html_form;
              bindFormSubmit();
            }
          });
      });
    }
  });
</script>
{% endblock %} {% endcomment %}
