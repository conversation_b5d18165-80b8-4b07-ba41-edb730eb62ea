{% extends 'base.html' %}
{% load static %}

{% block title %}Approve Project: {{ project.name }}{% endblock %}

{% block content %}
<div class="container mt-4">
  <div class="row justify-content-center">
    <div class="col-lg-8">
      <div class="card">
        <div class="card-header bg-success text-white">
          <h4 class="mb-0">
            <i class="bi bi-check-circle me-2"></i>Project Approval: {{ project.name }}
          </h4>
        </div>
        <div class="card-body">
          <!-- Project Summary -->
          <div class="mb-4">
            <h5 class="border-bottom pb-2">Project Summary</h5>
            <div class="row">
              <div class="col-md-6">
                <p><strong>Project Name:</strong> {{ project.name }}</p>
                <p><strong>Region:</strong> {{ project.region }}</p>
                <p><strong>Type:</strong> {{ project.get_type_display }}</p>
              </div>
              <div class="col-md-6">
                <p><strong>Vendor:</strong> {{ project.vendor.name }}</p>
                <p><strong>Vendor Level:</strong> {{ project.vendor_level }}</p>
                <p><strong>Status:</strong> {{ project.get_status_display }}</p>
              </div>
            </div>
            <div class="row">
              <div class="col-md-6">
                <p><strong>Start Date:</strong> {{ project.start_date }}</p>
              </div>
              <div class="col-md-6">
                <p><strong>End Date:</strong> {{ project.end_date }}</p>
              </div>
            </div>
          </div>
          
          <!-- Approval Form -->
          <form method="post">
            {% csrf_token %}
            
            {% if form.non_field_errors %}
            <div class="alert alert-danger">
              {% for error in form.non_field_errors %}
                {{ error }}
              {% endfor %}
            </div>
            {% endif %}
            
            <div class="form-check form-switch mb-3">
              <input class="form-check-input" type="checkbox" id="{{ form.approved.id_for_label }}" name="{{ form.approved.name }}" {% if form.approved.value %}checked{% endif %}>
              <label class="form-check-label" for="{{ form.approved.id_for_label }}">Approve this project</label>
              {% if form.approved.errors %}
                <div class="invalid-feedback d-block">
                  {% for error in form.approved.errors %}
                    {{ error }}
                  {% endfor %}
                </div>
              {% endif %}
            </div>
            
            <div class="row mb-3 approval-fields" {% if not form.approved.value %}style="display: none;"{% endif %}>
              <div class="col-md-6">
                <div class="form-group">
                  <label for="{{ form.approved_by.id_for_label }}" class="form-label">Approved By</label>
                  {{ form.approved_by }}
                  {% if form.approved_by.errors %}
                    <div class="invalid-feedback d-block">
                      {% for error in form.approved_by.errors %}
                        {{ error }}
                      {% endfor %}
                    </div>
                  {% endif %}
                </div>
              </div>
              <div class="col-md-6">
                <div class="form-group">
                  <label for="{{ form.approved_date.id_for_label }}" class="form-label">Approval Date</label>
                  {{ form.approved_date }}
                  {% if form.approved_date.errors %}
                    <div class="invalid-feedback d-block">
                      {% for error in form.approved_date.errors %}
                        {{ error }}
                      {% endfor %}
                    </div>
                  {% endif %}
                </div>
              </div>
            </div>
            
            <div class="d-flex justify-content-between mt-4">
              <a href="{% url 'project_detail' project.pk %}" class="btn btn-outline-secondary">
                <i class="bi bi-arrow-left me-1"></i>Back to Project
              </a>
              <button type="submit" class="btn btn-success">
                <i class="bi bi-check-circle me-1"></i>Save Approval Status
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>
  </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
  document.addEventListener('DOMContentLoaded', function() {
    // Show/hide approval fields based on checkbox
    const approvedCheckbox = document.getElementById('{{ form.approved.id_for_label }}');
    const approvalFields = document.querySelector('.approval-fields');
    
    approvedCheckbox.addEventListener('change', function() {
      if (this.checked) {
        approvalFields.style.display = 'flex';
      } else {
        approvalFields.style.display = 'none';
      }
    });
  });
</script>
{% endblock %}