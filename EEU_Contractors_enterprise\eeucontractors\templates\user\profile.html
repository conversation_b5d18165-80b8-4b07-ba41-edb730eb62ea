{% extends 'base.html' %}

{% block content %}
  <h2>Update Profile</h2>

  {% if messages %}
    {% for message in messages %}
      <div style="color: green;">{{ message }}</div>
    {% endfor %}
  {% endif %}

  <form method="POST">
    {% csrf_token %}

    <label for="first_name">First Name:</label><br>
    <input type="text" name="first_name" id="first_name" value="{{ user.first_name }}" required><br><br>

    <label for="last_name">Last Name:</label><br>
    <input type="text" name="last_name" id="last_name" value="{{ user.last_name }}" required><br><br>

    <!-- Remove email if you don't use it -->
    <label for="email">Email:</label><br>
    <input type="email" name="email" id="email" value="{{ user.email }}"><br><br>

    <button type="submit">Save Changes</button>
  </form>
{% endblock %}
