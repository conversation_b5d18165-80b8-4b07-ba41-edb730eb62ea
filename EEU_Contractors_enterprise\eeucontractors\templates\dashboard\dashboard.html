<!-- {% extends "base.html" %}
{% load static %}

{% block head %}
<link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet" />
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<style>
  body { background-color: #f8f9fa; }
  .card { box-shadow: 0 0.125rem 0.25rem rgb(0 0 0 / 0.075); border-radius: 0.5rem; }
  .filter-label { font-weight: 600; font-size: 0.9rem; } 
  

  .card {
    box-shadow: 0 0.125rem 0.25rem rgb(0 0 0 / 0.075);
    border-radius: 0.5rem;
    border: 2px solid orange;
    padding: 1rem 1.25rem !important;
    min-height: 280px;
  }

  .filter-label {
    font-weight: 600;
    font-size: 0.9rem;
    color: #198754; /* Green label text */
  }

  /* Updated title color */
  h1.text-primary {
    color: orange !important;
    font-weight: bold;
  }

  /* Updated primary button styling */
  .btn.btn-primary {
    background-color: #198754;   /* Green background */
    border: 2px solid orange;    /* Orange border */
    color: white;
  }

  .btn.btn-primary:hover {
    background-color: #145c3d;   /* Darker green on hover */
    border-color: darkorange;
  }

  /* Filter select boxes with orange border */
  select.form-select,
  .form-select {
    border: 2px solid orange;
    height: calc(2.3rem + 2px);
    border-radius: 0.375rem;
  }

  .card-title {
    color: #198754;
    font-weight: bold;
  }
</style>


{% endblock head %}

{% block content %}
<div class="container py-4">
  <h1 class="mb-4 text-primary">Project Dashboard</h1>

  Filters 
  <form method="get" class="row g-3 align-items-end mb-4">
    <div class="col-sm-6 col-md-4 col-lg-3">
      <label for="regionSelect" class="filter-label">Filter by Region</label>
      <select id="regionSelect" name="region" class="form-select">
        <option value="" {% if not selected_region %}selected{% endif %}>All Regions</option>
        {% for region in regions %}
          <option value="{{ region.id }}" {% if selected_region == region.id|stringformat:"s" %}selected{% endif %}>{{ region.name }}</option>
        {% endfor %}
      </select>
    </div>
    <div class="col-sm-6 col-md-4 col-lg-3">
      <label for="contractorTypeSelect" class="filter-label">Filter by Contractor Type</label>
      <select id="contractorTypeSelect" name="contractor_type" class="form-select">
        <option value="" {% if not selected_contractor_type %}selected{% endif %}>All Types</option>
        {% for code,label in contractor_types %}
          <option value="{{ code }}" {% if selected_contractor_type == code %}selected{% endif %}>{{ label }}</option>
        {% endfor %}
      </select>
    </div>
    <div class="col-auto">
      <button type="submit" class="btn btn-primary px-4">Apply</button>
    </div>
  </form>

  Charts 
  <div class="row g-4">
    <div class="col-lg-6">
      <div class="card p-4 bg-white">
        <h5 class="card-title mb-3">Average Progress (Last 30 Days)</h5>
        <canvas id="timeChart" style="height: 300px;"></canvas>
      </div>
    </div>
    <div class="col-lg-6">
      <div class="card p-4 bg-white">
        <h5 class="card-title mb-3">Completed Projects Per Day</h5>
        <canvas id="barChart" style="height: 300px;"></canvas>
      </div>
    </div>
    <div class="col-lg-12">
      <div class="card p-4 bg-white">
        <h5 class="card-title mb-3">Project Status Distribution</h5>
        <canvas id="statusChart" style="height: 350px;"></canvas>
      </div>
    </div>
  </div>
</div>
{% endblock content %}

{% block extra_js %}
<script>
  const timeData = {{ time_data|safe }};
  const statusData = {{ status_counts|safe }};
  const barData = {{ completed_bar_data|safe }};

  // Line chart
  const labelsTime = timeData.map(item => new Date(item.day).toLocaleDateString(undefined, {month:'short', day:'numeric'}));
  const avgProgress = timeData.map(item => parseFloat(item.avg_progress.toFixed(2)) || 0);
  new Chart(document.getElementById('timeChart').getContext('2d'), {
    type: 'line',
    data: {
      labels: labelsTime,
      datasets:[{
        label:'Avg Progress %',
        data:avgProgress,
        borderColor:'#0d6efd',
        backgroundColor:'rgba(13,110,253,0.2)',
        fill:true,
        tension:0.3
      }]
    },
    options: {
      responsive:true,
      scales:{
        y:{
          beginAtZero:true,
          max:100,
          ticks:{ callback: v=>v+'%' }
        }
      }
    }
  });

  // Bar chart
  const labelsBar = barData.map(item => new Date(item.day).toLocaleDateString(undefined, {month:'short', day:'numeric'}));
  const barCounts = barData.map(item => item.count);
  new Chart(document.getElementById('barChart').getContext('2d'), {
    type: 'bar',
    data: {
      labels: labelsBar,
      datasets:[{
        label:'Completed Projects',
        data:barCounts,
        backgroundColor:'rgba(25,135,84,0.7)',
        borderColor:'#198754',
        borderWidth:1
      }]
    },
    options: {
      responsive:true,
      scales:{
        y:{
          beginAtZero:true,
          ticks:{ stepSize:1 }
        }
      }
    }
  });

  // Pie chart
  const labelsStatus = statusData.map(item => item.status.charAt(0).toUpperCase() + item.status.slice(1));
  const countsStatus = statusData.map(item => item.count);
  const colors = ['#0d6efd','#ffc107','#198754','#dc3545'];
  new Chart(document.getElementById('statusChart').getContext('2d'), {
    type: 'pie',
    data: {
      labels: labelsStatus,
      datasets:[{
        data: countsStatus,
        backgroundColor: colors,
        hoverOffset: 30,
        borderColor: '#fff',
        borderWidth: 2
      }]
    },
    options: {
      responsive:true,
      plugins: {
        legend: {
          position: 'bottom',
          labels: { boxWidth: 18 }
        },
        tooltip: {
          callbacks: {
            label: ctx => `${ctx.label}: ${ctx.parsed} projects`
          }
        }
      }
    }
  });
</script>
{% endblock extra_js %} -->
