{% extends 'base.html' %} {% load static %} {% block extra_css %}
<style>
  .form-card {
    border-radius: 10px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    background: #ffffff;
    border: none;
    max-width: 800px;
    margin: 0 auto;
  }

  .form-header {
    background: linear-gradient(135deg, #2c3e50, #4ca1af);
    color: white;
    padding: 5px;
    border-radius: 10px 10px 0 0;
    margin-bottom: 0;
  }

  .form-logo {
    height: 25px;
    margin-right: 10px;
    vertical-align: middle;
  }

  .form-body {
    padding: 10px 15px;
  }

  /* Admin-like fieldset styling */
  .fieldset {
    border: 1px solid #e1e1e1;
    padding: 0;
    border-radius: 4px;
    margin-bottom: 10px;
    background-color: #fff;
  }

  .fieldset-title {
    background-color: #f8f9fa;
    padding: 8px 12px;
    margin: 0;
    border-bottom: 1px solid #e1e1e1;
    font-weight: 600;
    font-size: 0.9rem;
    color: #2c3e50;
  }

  .fieldset-body {
    padding: 10px;
  }

  /* Form controls */
  .form-row {
    display: flex;
    flex-wrap: wrap;
    margin-right: -5px;
    margin-left: -5px;
  }

  .form-group {
    padding-right: 5px;
    padding-left: 5px;
    margin-bottom: 8px;
  }

  .form-group label {
    display: block;
    font-size: 0.8rem;
    color: #495057;
    margin-bottom: 2px;
  }

  .form-control,
  .form-select {
    display: block;
    width: 100%;
    padding: 0.375rem 0.75rem;
    font-size: 0.9rem;
    line-height: 1.5;
    color: #495057;
    background-color: #fff;
    background-clip: padding-box;
    border: 1px solid #ced4da;
    border-radius: 0.25rem;
    transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
  }

  .form-check {
    padding-left: 1.8rem;
    margin-bottom: 5px;
  }

  .form-check .form-check-input {
    margin-left: -1.4rem;
    width: 1rem;
    height: 1rem;
  }

  .form-check-label {
    font-size: 0.85rem;
  }

  /* Submit button */
  .submit-row {
    padding: 8px 0;
    margin: 0;
    text-align: right;
  }

  .btn-submit {
    background-color: #417690;
    color: #fff;
    border: none;
    border-radius: 4px;
    padding: 6px 15px;
    font-size: 0.9rem;
  }

  .btn-submit:hover {
    background-color: #2c5674;
    color: #fff;
  }

  /* Responsive adjustments */
  @media (min-width: 768px) {
    .form-group-col-4 {
      flex: 0 0 33.333333%;
      max-width: 33.333333%;
    }

    .form-group-col-6 {
      flex: 0 0 50%;
      max-width: 50%;
    }

    .form-group-col-12 {
      flex: 0 0 100%;
      max-width: 100%;
    }
  }

  /* Success message styling */
  .success-message {
    background-color: #d4edda;
    color: #155724;
    border: 1px solid #c3e6cb;
    border-radius: 4px;
    padding: 10px 15px;
    margin-bottom: 15px;
    display: flex;
    align-items: center;
  }

  .success-message i {
    font-size: 1.2rem;
    margin-right: 10px;
  }
</style>
{% endblock %} {% block content %}
<div class="container py-0">
  <div class="row justify-content-center">
    <div class="col-lg-10">
      <div class="form-card">
        <h5 class="form-header text-center mb-0 py-1">
          <img
            src="{% static 'eeucontractors/images/eeu_logo.png' %}"
            alt="EEU Logo"
            class="form-logo"
          />
          የአዲስ ተቋራጭ ምዝገባ ቼክሊስት
        </h5>

        <div class="form-body">
          {% if success_message %}
          <div class="success-message">
            <i class="bi bi-check-circle-fill"></i>
            {{ success_message }}
          </div>
          {% endif %}

          <form method="post" class="needs-validation" novalidate>
            {% csrf_token %}

            <!-- Contractor Info Fieldset -->
            <div class="fieldset">
              <h6 class="fieldset-title">Contractor Info</h6>
              <div class="fieldset-body">
                <div class="form-row">
                  <div class="form-group form-group-col-6">
                    <label for="{{ form.contractor_type.id_for_label }}"
                      >{{ form.contractor_type.label }}</label
                    >
                    {{ form.contractor_type }}
                  </div>

                  <div class="form-group form-group-col-6">
                    <label for="{{ form.contractor_name.id_for_label }}"
                      >{{ form.contractor_name.label }}</label
                    >
                    {{ form.contractor_name }}
                  </div>

                  {% if form.identification_number.value %}
                  <div class="form-group form-group-col-6">
                    <label>{{ form.identification_number.label }}</label>
                    <input
                      type="text"
                      class="form-control"
                      value="{{ form.identification_number.value }}"
                      disabled
                    />
                  </div>
                  {% endif %}
                </div>
              </div>
            </div>

            <!-- Human Resources Fieldset -->
            <div class="fieldset">
              <h6 class="fieldset-title">Human Resources</h6>
              <div class="fieldset-body">
                <div class="form-row">
                  <div class="form-group form-group-col-4">
                    <div class="form-check">
                      {{ form.has_professional_license }}
                      <label
                        class="form-check-label"
                        for="{{ form.has_professional_license.id_for_label }}"
                      >
                        {{ form.has_professional_license.label }}
                      </label>
                    </div>
                  </div>

                  <div class="form-group form-group-col-4">
                    <label for="{{ form.electric_grade.id_for_label }}"
                      >የተፈቀደለት ደረጃ?</label
                    >
                    {{ form.electric_grade }}
                  </div>

                  <div class="form-group form-group-col-4">
                    <div class="form-check">
                      {{ form.has_human_resource }}
                      <label
                        class="form-check-label"
                        for="{{ form.has_human_resource.id_for_label }}"
                      >
                        {{ form.has_human_resource.label }}
                      </label>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <!-- Professional Staff Fieldset -->
            <div class="fieldset">
              <h6 class="fieldset-title">Professional Staff</h6>
              <div class="fieldset-body">
                <div class="form-row">
                  <div class="form-group form-group-col-12">
                    <h6 class="mb-3">
                      <i class="bi bi-people-fill me-2"></i>4. ያቀረቡት ባለሙያ/ከፊል
                      ባለሙያ ብዛት
                    </h6>
                  </div>

                  <div class="form-group form-group-col-4">
                    <label for="{{ form.electrical_engineers.id_for_label }}">
                      {{ form.electrical_engineers.label }}
                    </label>
                    {{ form.electrical_engineers }}
                  </div>

                  <div class="form-group form-group-col-4">
                    <label
                      for="{{ form.electromechanical_engineers.id_for_label }}"
                    >
                      {{ form.electromechanical_engineers.label }}
                    </label>
                    {{ form.electromechanical_engineers }}
                  </div>

                  <div class="form-group form-group-col-4">
                    <label for="{{ form.civil_engineers.id_for_label }}">
                      {{ form.civil_engineers.label }}
                    </label>
                    {{ form.civil_engineers }}
                  </div>

                  <div class="form-group form-group-col-4">
                    <label for="{{ form.electricians_foremen.id_for_label }}">
                      {{ form.electricians_foremen.label }}
                    </label>
                    {{ form.electricians_foremen }}
                  </div>

                  <div class="form-group form-group-col-4">
                    <label for="{{ form.surveyors.id_for_label }}">
                      {{ form.surveyors.label }}
                    </label>
                    {{ form.surveyors }}
                  </div>

                  <div class="form-group form-group-col-4">
                    <label for="{{ form.line_workers.id_for_label }}">
                      {{ form.line_workers.label }}
                    </label>
                    {{ form.line_workers }}
                  </div>
                </div>
              </div>
            </div>

            <!-- Document Checklist Fieldset -->
            <div class="fieldset">
              <h6 class="fieldset-title">Document Checklist</h6>
              <div class="fieldset-body">
                <div class="form-row">
                  <div class="form-group form-group-col-6">
                    <div class="form-check">
                      {{ form.has_work_experience_doc }}
                      <label
                        class="form-check-label"
                        for="{{ form.has_work_experience_doc.id_for_label }}"
                      >
                        {{ form.has_work_experience_doc.label }}
                      </label>
                    </div>
                  </div>

                  <div class="form-group form-group-col-6">
                    <div class="form-check">
                      {{ form.has_trade_license }}
                      <label
                        class="form-check-label"
                        for="{{ form.has_trade_license.id_for_label }}"
                      >
                        {{ form.has_trade_license.label }}
                      </label>
                    </div>
                  </div>

                  <div class="form-group form-group-col-6">
                    <div class="form-check">
                      {{ form.has_energy_certification }}
                      <label
                        class="form-check-label"
                        for="{{ form.has_energy_certification.id_for_label }}"
                      >
                        {{ form.has_energy_certification.label }}
                      </label>
                    </div>
                  </div>

                  <div class="form-group form-group-col-6">
                    <div class="form-check">
                      {{ form.has_equipment_resources }}
                      <label
                        class="form-check-label"
                        for="{{ form.has_equipment_resources.id_for_label }}"
                      >
                        {{ form.has_equipment_resources.label }}
                      </label>
                    </div>
                  </div>

                  <div class="form-group form-group-col-6">
                    <div class="form-check">
                      {{ form.has_vat_and_tax }}
                      <label
                        class="form-check-label"
                        for="{{ form.has_vat_and_tax.id_for_label }}"
                      >
                        {{ form.has_vat_and_tax.label }}
                      </label>
                    </div>
                  </div>

                  <div class="form-group form-group-col-6">
                    <div class="form-check">
                      {{ form.has_good_performance }}
                      <label
                        class="form-check-label"
                        for="{{ form.has_good_performance.id_for_label }}"
                      >
                        {{ form.has_good_performance.label }}
                      </label>
                    </div>
                  </div>

                  <div class="form-group form-group-col-6">
                    <div class="form-check">
                      {{ form.has_financial_capacity }}
                      <label
                        class="form-check-label"
                        for="{{ form.has_financial_capacity.id_for_label }}"
                      >
                        {{ form.has_financial_capacity.label }}
                      </label>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <!-- Registration Details Fieldset -->
            <div class="fieldset">
              <h6 class="fieldset-title">Registration Details</h6>
              <div class="fieldset-body">
                <div class="form-row">
                  <!-- is_registered field removed as it no longer exists in the model -->

                  <div class="form-group form-group-col-4">
                    <label for="{{ form.registrar_name.id_for_label }}"
                      >{{ form.registrar_name.label }}</label
                    >
                    {{ form.registrar_name }}
                  </div>

                  <div class="form-group form-group-col-4">
                    <label for="{{ form.registrar_id.id_for_label }}"
                      >{{ form.registrar_id.label }}</label
                    >
                    {{ form.registrar_id }}
                  </div>
                </div>
              </div>
            </div>

            <!-- Submit Row -->
            <div class="submit-row">
              <button type="submit" class="btn btn-submit">
                <i class="bi bi-check-circle me-1"></i>አስገባ
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>
  </div>
</div>
{% endblock %} {% block extra_js %}
<script>
  document.addEventListener("DOMContentLoaded", function () {
    const forms = document.querySelectorAll(".needs-validation");

    Array.from(forms).forEach((form) => {
      form.addEventListener(
        "submit",
        (event) => {
          if (!form.checkValidity()) {
            event.preventDefault();
            event.stopPropagation();
          }

          form.classList.add("was-validated");
        },
        false
      );
    });
  });
</script>
{% endblock %}
