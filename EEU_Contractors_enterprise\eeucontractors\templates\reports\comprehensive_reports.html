{% extends 'base.html' %}
{% load static %}

{% block content %}
<div class="container-fluid mt-4">
  <!-- <PERSON> Header -->
  <div class="row mb-4">
    <div class="col-12">
      <h2 class="text-success fw-bold">Comprehensive Reports</h2>
      <p class="text-muted">Detailed analysis of contractors, activities, and progress</p>
    </div>
  </div>

  <!-- Report Navigation Tabs -->
  <ul class="nav nav-tabs mb-4" id="reportTabs" role="tablist">
    <li class="nav-item" role="presentation">
      <button class="nav-link active" id="contractors-tab" data-bs-toggle="tab" data-bs-target="#contractors" type="button" role="tab">
        <i class="bi bi-building me-2"></i>Contractors Overview
      </button>
    </li>
    <li class="nav-item" role="presentation">
      <button class="nav-link" id="involved-tab" data-bs-toggle="tab" data-bs-target="#involved" type="button" role="tab">
        <i class="bi bi-people me-2"></i>Currently Involved
      </button>
    </li>
    <li class="nav-item" role="presentation">
      <button class="nav-link" id="activities-tab" data-bs-toggle="tab" data-bs-target="#activities" type="button" role="tab">
        <i class="bi bi-list-task me-2"></i>Activities Progress
      </button>
    </li>
  </ul>

  <div class="tab-content" id="reportTabsContent">
    
    <!-- Tab 1: Contractors Overview -->
    <div class="tab-pane fade show active" id="contractors" role="tabpanel">
      <div class="card">
        <div class="card-header bg-success text-white">
          <h5 class="mb-0"><i class="bi bi-building me-2"></i>Contractors & Enterprises Overview</h5>
        </div>
        <div class="card-body">
          <!-- Search & Filter -->
          <form method="get" class="row g-3 mb-4">
            <input type="hidden" name="tab" value="contractors">
            <div class="col-md-4">
              <label class="form-label">Search Contractor</label>
              <input type="text" class="form-control" name="search_contractor" 
                     placeholder="Enter contractor name..." value="{{ request.GET.search_contractor }}">
            </div>
            <div class="col-md-3">
              <label class="form-label">Type</label>
              <select class="form-select" name="contractor_type">
                <option value="">All Types</option>
                <option value="contractor" {% if request.GET.contractor_type == "contractor" %}selected{% endif %}>Contractor</option>
                <option value="enterprise" {% if request.GET.contractor_type == "enterprise" %}selected{% endif %}>Enterprise</option>
                <option value="new_enterprise" {% if request.GET.contractor_type == "new_enterprise" %}selected{% endif %}>New Enterprise</option>
              </select>
            </div>
            <div class="col-md-3">
              <label class="form-label">Region</label>
              <select class="form-select" name="region">
                <option value="">All Regions</option>
                {% for region in all_regions %}
                <option value="{{ region.id }}" {% if request.GET.region == region.id|stringformat:"s" %}selected{% endif %}>
                  {{ region.name }}
                </option>
                {% endfor %}
              </select>
            </div>
            <div class="col-md-2">
              <label class="form-label">&nbsp;</label>
              <div class="d-grid">
                <button type="submit" class="btn btn-warning">
                  <i class="bi bi-search"></i> Search
                </button>
              </div>
            </div>
          </form>

          <!-- Summary Cards -->
          <div class="row mb-4">
            <div class="col-md-3">
              <div class="card border-success">
                <div class="card-body text-center">
                  <h4 class="text-success">{{ contractor_stats.total_contractors }}</h4>
                  <small class="text-muted">Total Contractors</small>
                </div>
              </div>
            </div>
            <div class="col-md-3">
              <div class="card border-warning">
                <div class="card-body text-center">
                  <h4 class="text-warning">{{ contractor_stats.total_enterprises }}</h4>
                  <small class="text-muted">Total Enterprises</small>
                </div>
              </div>
            </div>
            <div class="col-md-3">
              <div class="card border-info">
                <div class="card-body text-center">
                  <h4 class="text-info">{{ contractor_stats.new_enterprises }}</h4>
                  <small class="text-muted">New Enterprises</small>
                </div>
              </div>
            </div>
            <div class="col-md-3">
              <div class="card border-success">
                <div class="card-body text-center">
                  <h4 class="text-success">{{ contractor_stats.active_count }}</h4>
                  <small class="text-muted">Currently Active</small>
                </div>
              </div>
            </div>
          </div>

          <!-- Contractors Table -->
          <div class="table-responsive">
            <table class="table table-hover">
              <thead class="table-light">
                <tr>
                  <th>Contractor Name</th>
                  <th>Type</th>
                  <th>Region</th>
                  <th>Electric Grade</th>
                  <th>Experience</th>
                  <th>Phone</th>
                  <th>Status</th>
                  <th>Registration Date</th>
                </tr>
              </thead>
              <tbody>
                {% for contractor in contractors_list %}
                <tr>
                  <td><strong>{{ contractor.contractor_name }}</strong></td>
                  <td>
                    <span class="badge {% if contractor.contractor_type == 'contractor' %}bg-success{% elif contractor.contractor_type == 'enterprise' %}bg-warning{% else %}bg-info{% endif %}">
                      {{ contractor.get_contractor_type_display }}
                    </span>
                  </td>
                  <td>{{ contractor.region.name }}</td>
                  <td>{{ contractor.electric_grade }}</td>
                  <td>{{ contractor.experience }} years</td>
                  <td>{{ contractor.phone_number }}</td>
                  <td>
                    {% if contractor.vendor_set.first %}
                      <span class="badge {% if contractor.vendor_set.first.status == 'active' %}bg-success{% else %}bg-secondary{% endif %}">
                        {{ contractor.vendor_set.first.get_status_display }}
                      </span>
                    {% else %}
                      <span class="badge bg-secondary">Not Set</span>
                    {% endif %}
                  </td>
                  <td>{{ contractor.registration_date|date:"M d, Y" }}</td>
                </tr>
                {% empty %}
                <tr>
                  <td colspan="8" class="text-center text-muted">No contractors found</td>
                </tr>
                {% endfor %}
              </tbody>
            </table>
          </div>
        </div>
      </div>
    </div>

    <!-- Tab 2: Currently Involved -->
    <div class="tab-pane fade" id="involved" role="tabpanel">
      <div class="card">
        <div class="card-header bg-warning text-white">
          <h5 class="mb-0"><i class="bi bi-people me-2"></i>Currently Involved Contractors</h5>
        </div>
        <div class="card-body">
          <!-- Search & Filter -->
          <form method="get" class="row g-3 mb-4">
            <input type="hidden" name="tab" value="involved">
            <div class="col-md-4">
              <label class="form-label">Search Project/Contractor</label>
              <input type="text" class="form-control" name="search_involved" 
                     placeholder="Enter project or contractor name..." value="{{ request.GET.search_involved }}">
            </div>
            <div class="col-md-3">
              <label class="form-label">Project Status</label>
              <select class="form-select" name="project_status">
                <option value="">All Status</option>
                <option value="on-going" {% if request.GET.project_status == "on-going" %}selected{% endif %}>On-going</option>
                <option value="completed" {% if request.GET.project_status == "completed" %}selected{% endif %}>Completed</option>
                <option value="delayed" {% if request.GET.project_status == "delayed" %}selected{% endif %}>Delayed</option>
                <option value="suspended" {% if request.GET.project_status == "suspended" %}selected{% endif %}>Suspended</option>
              </select>
            </div>
            <div class="col-md-3">
              <label class="form-label">Main Activity</label>
              <select class="form-select" name="main_activity">
                <option value="">All Activities</option>
                {% for activity in all_main_activities %}
                <option value="{{ activity.id }}" {% if request.GET.main_activity == activity.id|stringformat:"s" %}selected{% endif %}>
                  {{ activity.get_main_activity_display }}
                </option>
                {% endfor %}
              </select>
            </div>
            <div class="col-md-2">
              <label class="form-label">&nbsp;</label>
              <div class="d-grid">
                <button type="submit" class="btn btn-success">
                  <i class="bi bi-search"></i> Search
                </button>
              </div>
            </div>
          </form>

          <!-- Currently Involved Table -->
          <div class="table-responsive">
            <table class="table table-hover">
              <thead class="table-light">
                <tr>
                  <th>Project</th>
                  <th>Contractor</th>
                  <th>Type</th>
                  <th>Main Activity</th>
                  <th>Progress</th>
                  <th>Status</th>
                  <th>Start Date</th>
                  <th>Expected End</th>
                  <th>Action</th>
                </tr>
              </thead>
              <tbody>
                {% for progress in involved_contractors %}
                <tr>
                  <td><strong>{{ progress.project.name }}</strong></td>
                  <td>{{ progress.vendor.name }}</td>
                  <td>
                    <span class="badge {% if progress.vendor.vendor_registration.contractor_type == 'contractor' %}bg-success{% elif progress.vendor.vendor_registration.contractor_type == 'enterprise' %}bg-warning{% else %}bg-info{% endif %}">
                      {{ progress.vendor.vendor_registration.get_contractor_type_display }}
                    </span>
                  </td>
                  <td>{{ progress.get_main_activity_display }}</td>
                  <td>
                    <div class="progress" style="height: 20px; min-width: 100px;">
                      <div class="progress-bar bg-success" style="width: {{ progress.progress_percentage }}%;">
                        {{ progress.progress_percentage }}%
                      </div>
                    </div>
                  </td>
                  <td>
                    <span class="badge {% if progress.status == 'completed' %}bg-success{% elif progress.status == 'on-going' %}bg-warning{% elif progress.status == 'delayed' %}bg-danger{% else %}bg-secondary{% endif %}">
                      {{ progress.get_status_display }}
                    </span>
                  </td>
                  <td>{{ progress.start_date|date:"M d, Y" }}</td>
                  <td>{{ progress.project.estimated_end_date|date:"M d, Y"|default:"Not set" }}</td>
                  <td>
                    <a href="{% url 'select_project_progress' progress_id=progress.id %}" 
                       class="text-decoration-underline" 
                       style="color: orange; font-weight: bold;">
                      View Details
                    </a>
                  </td>
                </tr>
                {% empty %}
                <tr>
                  <td colspan="9" class="text-center text-muted">No active projects found</td>
                </tr>
                {% endfor %}
              </tbody>
            </table>
          </div>
        </div>
      </div>
    </div>

    <!-- Tab 3: Activities Progress -->
    <div class="tab-pane fade" id="activities" role="tabpanel">
      <div class="card">
        <div class="card-header bg-info text-white">
          <h5 class="mb-0"><i class="bi bi-list-task me-2"></i>Main Activities Progress Tracking</h5>
        </div>
        <div class="card-body">
          <!-- Search & Filter -->
          <form method="get" class="row g-3 mb-4">
            <input type="hidden" name="tab" value="activities">
            <div class="col-md-3">
              <label class="form-label">Date From</label>
              <input type="date" class="form-control" name="date_from" value="{{ request.GET.date_from }}">
            </div>
            <div class="col-md-3">
              <label class="form-label">Date To</label>
              <input type="date" class="form-control" name="date_to" value="{{ request.GET.date_to }}">
            </div>
            <div class="col-md-3">
              <label class="form-label">Main Activity</label>
              <select class="form-select" name="activity_filter">
                <option value="">All Activities</option>
                {% for activity in all_main_activities %}
                <option value="{{ activity.id }}" {% if request.GET.activity_filter == activity.id|stringformat:"s" %}selected{% endif %}>
                  {{ activity.get_main_activity_display }}
                </option>
                {% endfor %}
              </select>
            </div>
            <div class="col-md-3">
              <label class="form-label">Time Period</label>
              <select class="form-select" name="time_period">
                <option value="week" {% if request.GET.time_period == "week" %}selected{% endif %}>Per Week</option>
                <option value="month" {% if request.GET.time_period == "month" %}selected{% endif %}>Per Month</option>
                <option value="year" {% if request.GET.time_period == "year" %}selected{% endif %}>Per Year</option>
              </select>
            </div>
            <div class="col-12">
              <button type="submit" class="btn btn-info">
                <i class="bi bi-search"></i> Generate Report
              </button>
              <a href="{% url 'comprehensive_reports' %}?tab=activities" class="btn btn-outline-secondary ms-2">
                <i class="bi bi-arrow-clockwise"></i> Reset
              </a>
            </div>
          </form>

          <!-- Activities Progress Table -->
          <div class="table-responsive">
            <table class="table table-hover">
              <thead class="table-light">
                <tr>
                  <th>Main Activity</th>
                  <th>Sub-Activities</th>
                  <th>Unit Type</th>
                  <th>Total Quantity</th>
                  <th>Completed</th>
                  <th>Progress %</th>
                  <th>Completion Rate</th>
                  <th>Period</th>
                  <th>Remarks</th>
                </tr>
              </thead>
              <tbody>
                {% for activity in activities_progress %}
                <tr>
                  <td><strong>{{ activity.main_activity_name }}</strong></td>
                  <td>{{ activity.sub_activities_count }} sub-activities</td>
                  <td>{{ activity.unit_types|join:", " }}</td>
                  <td>{{ activity.total_quantity }}</td>
                  <td>{{ activity.completed_quantity }}</td>
                  <td>
                    <div class="progress" style="height: 18px; min-width: 80px;">
                      <div class="progress-bar bg-info" style="width: {{ activity.progress_percentage }}%;">
                        {{ activity.progress_percentage|floatformat:1 }}%
                      </div>
                    </div>
                  </td>
                  <td>
                    <span class="badge {% if activity.completion_rate >= 100 %}bg-success{% elif activity.completion_rate >= 75 %}bg-warning{% else %}bg-danger{% endif %}">
                      {{ activity.completion_rate|floatformat:1 }}% {{ request.GET.time_period|default:"week" }}
                    </span>
                  </td>
                  <td>{{ activity.period_display }}</td>
                  <td>
                    <small class="text-muted">
                      {% if activity.completion_rate >= 100 %}
                        Excellent progress
                      {% elif activity.completion_rate >= 75 %}
                        Good progress
                      {% elif activity.completion_rate >= 50 %}
                        Moderate progress
                      {% else %}
                        Needs attention
                      {% endif %}
                    </small>
                  </td>
                </tr>
                {% empty %}
                <tr>
                  <td colspan="9" class="text-center text-muted">No activities found for the selected criteria</td>
                </tr>
                {% endfor %}
              </tbody>
            </table>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<script>
// Handle tab switching with URL parameters
document.addEventListener('DOMContentLoaded', function() {
    const urlParams = new URLSearchParams(window.location.search);
    const activeTab = urlParams.get('tab');
    
    if (activeTab) {
        const tabButton = document.getElementById(activeTab + '-tab');
        const tabPane = document.getElementById(activeTab);
        
        if (tabButton && tabPane) {
            // Remove active from all tabs
            document.querySelectorAll('.nav-link').forEach(tab => tab.classList.remove('active'));
            document.querySelectorAll('.tab-pane').forEach(pane => {
                pane.classList.remove('show', 'active');
            });
            
            // Activate selected tab
            tabButton.classList.add('active');
            tabPane.classList.add('show', 'active');
        }
    }
});
</script>

{% endblock %}
