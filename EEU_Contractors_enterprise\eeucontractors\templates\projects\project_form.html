{% extends "base.html" %}
{% load static %}

{% block title %}
{% if edit %}Edit Project{% else %}Create Project{% endif %}
{% endblock %}

{% block content %}
<style>
  /* Entire page background */
  body {
    background: linear-gradient(135deg, #fff7ed 0%, #f3faf7 100%);
    /* very light warm orange top left to very light green bottom right */
    min-height: 100vh;
  }

  /* Soft pastel green and orange highlights, very subtle */
  .card {
    background-color: #fcfcfb; /* off-white with warm tint */
    border: 1.5px solid #d6e7d1; /* very light green border */
    box-shadow: 0 0.15rem 0.3rem rgba(242, 168, 71, 0.1); /* subtle orange shadow */
    transition: box-shadow 0.3s ease;
  }
  .card:hover {
    box-shadow: 0 0.25rem 0.5rem rgba(242, 168, 71, 0.3);
  }

  /* Title with letter spacing and vertical gradient text color */
  h3 {
    font-family: "Times New Roman", Times, serif;
    font-weight: 600;
    margin-bottom: 1rem;

    /* letter spacing */
    letter-spacing: 2px;

    /* gradient text */
    background: linear-gradient(to bottom, #f2a847, #4a604a);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
  }

  label.form-label {
    color: #667a5a; /* muted green */
    font-weight: 600;
  }

  .form-check-label {
    color: #8b6f3f; /* muted orange */
    font-weight: 500;
  }

  input.form-control,
  select.form-select {
    border: 1px solid #d9decf; /* light olive-greenish border */
    background-color: #fafaf7; /* very light warm background */
    transition: border-color 0.25s ease;
  }
  input.form-control:focus,
  select.form-select:focus {
    border-color: #f2a847; /* soft orange */
    box-shadow: 0 0 5px rgba(242, 168, 71, 0.3);
    background-color: #fffefc;
  }

  .btn-primary {
    background-color: #e7a853; /* soft orange */
    border-color: #c78932;
  }
  .btn-primary:hover {
    background-color: #f2b86f;
    border-color: #d19e47;
  }

  .btn-secondary {
    background-color: #a3b687; /* muted green */
    border-color: #8a9e6e;
    color: white;
  }
  .btn-secondary:hover {
    background-color: #92a16e;
    border-color: #7f925d;
  }
</style>

<div class="container" style="max-width: 900px">
  <div class="card shadow-sm rounded-4 p-4 mt-0">
    <h3 class="mb-2">{% if edit %}Edit{% else %}Create{% endif %} Project</h3>

    <form method="post" enctype="multipart/form-data">
      {% csrf_token %}
      
      <div class="row g-1">
        {% for field in form %}
          {% if field.name == 'approved' %}
            <div class="col-md-6 mb-1">
              <div class="form-check mt-2">
                {{ field }}
                <label for="{{ field.id_for_label }}" class="form-check-label">
                  {{ field.label }}
                </label>
              </div>
              {% if field.errors %}
                <div class="text-danger small py-0 my-0">{{ field.errors }}</div>
              {% endif %}
            </div>
          {% elif field.name == 'level' or field.name == 'main_activity' %}
            <div class="col-md-6 mb-1">
              <label for="{{ field.id_for_label }}" class="form-label fw-bold mb-0 small">{{ field.label }}</label>
              {{ field }}
              {% if field.errors %}
                <div class="text-danger small py-0 my-0">{{ field.errors }}</div>
              {% endif %}
            </div>
          {% else %}
            <div class="col-md-6 mb-1">
              <label for="{{ field.id_for_label }}" class="form-label fw-bold mb-0 small">{{ field.label }}</label>
              {{ field }}
              {% if field.help_text %}
                <div class="form-text text-muted small py-0 my-0">{{ field.help_text }}</div>
              {% endif %}
              {% if field.errors %}
                <div class="text-danger small py-0 my-0">{{ field.errors }}</div>
              {% endif %}
            </div>
          {% endif %}
        {% endfor %}
      </div>

      <div class="d-flex justify-content-between mt-2">
        <a href="{% url 'project_list' %}" class="btn btn-secondary btn-sm">
          <i class="bi bi-arrow-left"></i> Back
        </a>
        <button type="submit" class="btn btn-primary btn-sm">
          <i class="bi bi-save"></i> {% if edit %}Update{% else %}Create{% endif %}
        </button>
      </div>
    </form>
  </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
  document.addEventListener('DOMContentLoaded', function() {
    // Add placeholders to level and main_activity fields
    const levelField = document.querySelector('[name="level"]');
    if (levelField) {
      if (levelField.tagName === 'SELECT') {
        const firstOption = levelField.querySelector('option[value=""]');
        if (firstOption) {
          firstOption.textContent = "Select level (1-8)";
        } else {
          const placeholderOption = document.createElement('option');
          placeholderOption.value = "";
          placeholderOption.textContent = "Select level (1-8)";
          placeholderOption.selected = true;
          placeholderOption.disabled = true;
          levelField.insertBefore(placeholderOption, levelField.firstChild);
        }
      } else {
        levelField.placeholder = "Enter level (1-8)";
      }
    }
    
    const mainActivityField = document.querySelector('[name="main_activity"]');
    if (mainActivityField && mainActivityField.tagName === 'SELECT') {
      const firstOption = mainActivityField.querySelector('option[value=""]');
      if (firstOption) {
        firstOption.textContent = "Select main activity";
      } else {
        const placeholderOption = document.createElement('option');
        placeholderOption.value = "";
        placeholderOption.textContent = "Select main activity";
        placeholderOption.selected = true;
        placeholderOption.disabled = true;
        mainActivityField.insertBefore(placeholderOption, mainActivityField.firstChild);
      }
    }
    
    // Style the approved checkbox
    const approvedField = document.querySelector('[name="approved"]');
    if (approvedField) {
      approvedField.classList.add('form-check-input');
    }
  });
</script>
{% endblock %}





{% comment %} {% extends "base.html" %}
{% load static %}

{% block title %}
{% if edit %}Edit Project{% else %}Create Project{% endif %}
{% endblock %}

{% block content %}
<style>
  /* Soft pastel green and orange highlights, very subtle */
  .card {
    background-color: #fcfcfb; /* off-white with warm tint */
    border: 1.5px solid #d6e7d1; /* very light green border */
    box-shadow: 0 0.15rem 0.3rem rgba(242, 168, 71, 0.1); /* subtle orange shadow */
    transition: box-shadow 0.3s ease;
  }
  .card:hover {
    box-shadow: 0 0.25rem 0.5rem rgba(242, 168, 71, 0.3);
  }

  h3 {
    font-family: "Times New Roman", Times, serif;
    color: #4a604a; /* dark subtle green */
    font-weight: 600;
    margin-bottom: 1rem;
  }

  label.form-label {
    color: #667a5a; /* muted green */
    font-weight: 600;
  }

  .form-check-label {
    color: #8b6f3f; /* muted orange */
    font-weight: 500;
  }

  input.form-control,
  select.form-select {
    border: 1px solid #d9decf; /* light olive-greenish border */
    background-color: #fafaf7; /* very light warm background */
    transition: border-color 0.25s ease;
  }
  input.form-control:focus,
  select.form-select:focus {
    border-color: #f2a847; /* soft orange */
    box-shadow: 0 0 5px rgba(242, 168, 71, 0.3);
    background-color: #fffefc;
  }

  .btn-primary {
    background-color: #e7a853; /* soft orange */
    border-color: #c78932;
  }
  .btn-primary:hover {
    background-color: #f2b86f;
    border-color: #d19e47;
  }

  .btn-secondary {
    background-color: #a3b687; /* muted green */
    border-color: #8a9e6e;
    color: white;
  }
  .btn-secondary:hover {
    background-color: #92a16e;
    border-color: #7f925d;
  }
</style>

<div class="container" style="max-width: 900px">
  <div class="card shadow-sm rounded-4 p-4 mt-0">
    <h3 class="mb-2">{% if edit %}Edit{% else %}Create{% endif %} Project</h3>

    <form method="post" enctype="multipart/form-data">
      {% csrf_token %}
      
      <div class="row g-1">
        {% for field in form %}
          {% if field.name == 'approved' %}
            <div class="col-md-6 mb-1">
              <div class="form-check mt-2">
                {{ field }}
                <label for="{{ field.id_for_label }}" class="form-check-label">
                  {{ field.label }}
                </label>
              </div>
              {% if field.errors %}
                <div class="text-danger small py-0 my-0">{{ field.errors }}</div>
              {% endif %}
            </div>
          {% elif field.name == 'level' or field.name == 'main_activity' %}
            <div class="col-md-6 mb-1">
              <label for="{{ field.id_for_label }}" class="form-label fw-bold mb-0 small">{{ field.label }}</label>
              {{ field }}
              {% if field.errors %}
                <div class="text-danger small py-0 my-0">{{ field.errors }}</div>
              {% endif %}
            </div>
          {% else %}
            <div class="col-md-6 mb-1">
              <label for="{{ field.id_for_label }}" class="form-label fw-bold mb-0 small">{{ field.label }}</label>
              {{ field }}
              {% if field.help_text %}
                <div class="form-text text-muted small py-0 my-0">{{ field.help_text }}</div>
              {% endif %}
              {% if field.errors %}
                <div class="text-danger small py-0 my-0">{{ field.errors }}</div>
              {% endif %}
            </div>
          {% endif %}
        {% endfor %}
      </div>

      <div class="d-flex justify-content-between mt-2">
        <a href="{% url 'project_list' %}" class="btn btn-secondary btn-sm">
          <i class="bi bi-arrow-left"></i> Back
        </a>
        <button type="submit" class="btn btn-primary btn-sm">
          <i class="bi bi-save"></i> {% if edit %}Update{% else %}Create{% endif %}
        </button>
      </div>
    </form>
  </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
  document.addEventListener('DOMContentLoaded', function() {
    // Add placeholders to level and main_activity fields
    const levelField = document.querySelector('[name="level"]');
    if (levelField) {
      if (levelField.tagName === 'SELECT') {
        const firstOption = levelField.querySelector('option[value=""]');
        if (firstOption) {
          firstOption.textContent = "Select level (1-8)";
        } else {
          const placeholderOption = document.createElement('option');
          placeholderOption.value = "";
          placeholderOption.textContent = "Select level (1-8)";
          placeholderOption.selected = true;
          placeholderOption.disabled = true;
          levelField.insertBefore(placeholderOption, levelField.firstChild);
        }
      } else {
        levelField.placeholder = "Enter level (1-8)";
      }
    }
    
    const mainActivityField = document.querySelector('[name="main_activity"]');
    if (mainActivityField && mainActivityField.tagName === 'SELECT') {
      const firstOption = mainActivityField.querySelector('option[value=""]');
      if (firstOption) {
        firstOption.textContent = "Select main activity";
      } else {
        const placeholderOption = document.createElement('option');
        placeholderOption.value = "";
        placeholderOption.textContent = "Select main activity";
        placeholderOption.selected = true;
        placeholderOption.disabled = true;
        mainActivityField.insertBefore(placeholderOption, mainActivityField.firstChild);
      }
    }
    
    // Style the approved checkbox
    const approvedField = document.querySelector('[name="approved"]');
    if (approvedField) {
      approvedField.classList.add('form-check-input');
    }
  });
</script>
{% endblock %} {% endcomment %}






{% comment %} {% extends "base.html" %}
{% load static %}

{% block title %}
{% if edit %}Edit Project{% else %}Create Project{% endif %}
{% endblock %}

{% block content %}
<div class="container" style="max-width: 900px">
  <div class="card shadow-sm rounded-4 p-4 mt-0">
    <h3 class="mb-2">{% if edit %}Edit{% else %}Create{% endif %} Project</h3>

    <form method="post" enctype="multipart/form-data">
      {% csrf_token %}
      
      <div class="row g-1">
        {% for field in form %}
          {% if field.name == 'approved' %}
            <div class="col-md-6 mb-1">
              <div class="form-check mt-2">
                {{ field }}
                <label for="{{ field.id_for_label }}" class="form-check-label">
                  {{ field.label }}
                </label>
              </div>
              {% if field.errors %}
                <div class="text-danger small py-0 my-0">{{ field.errors }}</div>
              {% endif %}
            </div>
          {% elif field.name == 'level' or field.name == 'main_activity' %}
            <div class="col-md-6 mb-1">
              <label for="{{ field.id_for_label }}" class="form-label fw-bold mb-0 small">{{ field.label }}</label>
              {{ field }}
              {% if field.errors %}
                <div class="text-danger small py-0 my-0">{{ field.errors }}</div>
              {% endif %}
            </div>
          {% else %}
            <div class="col-md-6 mb-1">
              <label for="{{ field.id_for_label }}" class="form-label fw-bold mb-0 small">{{ field.label }}</label>
              {{ field }}
              {% if field.help_text %}
                <div class="form-text text-muted small py-0 my-0">{{ field.help_text }}</div>
              {% endif %}
              {% if field.errors %}
                <div class="text-danger small py-0 my-0">{{ field.errors }}</div>
              {% endif %}
            </div>
          {% endif %}
        {% endfor %}
      </div>

      <div class="d-flex justify-content-between mt-2">
        <a href="{% url 'project_list' %}" class="btn btn-secondary btn-sm">
          <i class="bi bi-arrow-left"></i> Back
        </a>
        <button type="submit" class="btn btn-primary btn-sm">
          <i class="bi bi-save"></i> {% if edit %}Update{% else %}Create{% endif %}
        </button>
      </div>
    </form>
  </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
  document.addEventListener('DOMContentLoaded', function() {
    // Add placeholders to level and main_activity fields
    const levelField = document.querySelector('[name="level"]');
    if (levelField) {
      if (levelField.tagName === 'SELECT') {
        // If it's a select field, we can't use placeholder directly
        const firstOption = levelField.querySelector('option[value=""]');
        if (firstOption) {
          firstOption.textContent = "Select level (1-8)";
        } else {
          const placeholderOption = document.createElement('option');
          placeholderOption.value = "";
          placeholderOption.textContent = "Select level (1-8)";
          placeholderOption.selected = true;
          placeholderOption.disabled = true;
          levelField.insertBefore(placeholderOption, levelField.firstChild);
        }
      } else {
        // If it's an input field
        levelField.placeholder = "Enter level (1-8)";
      }
    }
    
    const mainActivityField = document.querySelector('[name="main_activity"]');
    if (mainActivityField && mainActivityField.tagName === 'SELECT') {
      const firstOption = mainActivityField.querySelector('option[value=""]');
      if (firstOption) {
        firstOption.textContent = "Select main activity";
      } else {
        const placeholderOption = document.createElement('option');
        placeholderOption.value = "";
        placeholderOption.textContent = "Select main activity";
        placeholderOption.selected = true;
        placeholderOption.disabled = true;
        mainActivityField.insertBefore(placeholderOption, mainActivityField.firstChild);
      }
    }
    
    // Style the approved checkbox
    const approvedField = document.querySelector('[name="approved"]');
    if (approvedField) {
      approvedField.classList.add('form-check-input');
    }
  });
</script>
{% endblock %} {% comment %} {% extends "base.html" %} {% block content %}
<div class="container mt-4">
  <h2>{{ edit|default:False|yesno:"Edit Project,Create New Project" }}</h2>
  <form method="post">
    {% csrf_token %} {{ form.non_field_errors }} {% for field in form %}
    <div class="mb-3">
      <label class="form-label">{{ field.label }}:</label>
      {{ field }} {% if field.help_text %}
      <div class="form-text">{{ field.help_text }}</div>
      {% endif %} {% for error in field.errors %}
      <div class="text-danger">{{ error }}</div>
      {% endfor %}
    </div>
    {% endfor %}
    <button type="submit" class="btn btn-primary">Save</button>
    <a href="{% url 'project_list' %}" class="btn btn-secondary">Cancel</a>
  </form>
</div>
{% endblock %} {% endcomment %}

