{% extends 'base.html' %} {% block content %}

<h2>Edit Project Progress</h2>

<form method="post">
  {% csrf_token %} {{ form.as_p }} {{ formset.management_form }}

  <table class="table">
    <thead>
      <tr>
        <th>Sub Activity</th>
        <th>Progress</th>
        <th>Start Date</th>
        <th>End Date</th>
      </tr>
    </thead>
    <tbody>
      {% for form in formset %}
      <tr>
        <td>{{ form.instance.sub_activity }}</td>
        <td>{{ form.progress }}</td>
        <td>{{ form.start_date }}</td>
        <td>{{ form.end_date }}</td>
        {{ form.sub_activity }} {# hidden input #}
      </tr>
      {% endfor %}
    </tbody>
  </table>

  <button type="submit" class="btn btn-success">Save Changes</button>
  <a href="{% url 'progress_list' %}" class="btn btn-secondary">Cancel</a>
</form>

{% endblock %}
