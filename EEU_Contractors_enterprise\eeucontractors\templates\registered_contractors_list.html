{% extends 'base.html' %} {% load static %} {% block extra_css %}
<style>
  /* Admin-like styling */
  body {
    font-family: "Segoe UI", <PERSON><PERSON>, "Helvetica Neue", Arial, sans-serif;
  }

  .module {
    margin-bottom: 20px;
    background: #fff;
    border-radius: 4px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  }

  .module h2 {
    background: #79aec8;
    color: #fff;
    font-size: 14px;
    font-weight: 400;
    margin: 0;
    padding: 8px 10px;
    border-radius: 4px 4px 0 0;
  }

  .changelist {
    width: 100%;
  }

  .changelist-actions {
    padding: 10px;
    background: #f8f8f8;
    border-bottom: 1px solid #eee;
  }

  #changelist-form {
    overflow-x: auto;
  }

  #changelist table {
    width: 100%;
    border-collapse: collapse;
  }

  #changelist table thead th {
    padding: 8px;
    text-align: left;
    font-size: 13px;
    font-weight: 600;
    background: #f8f8f8;
    border-bottom: 1px solid #eee;
    color: #666;
  }

  #changelist table tbody tr {
    border-bottom: 1px solid #eee;
  }

  #changelist table tbody tr:hover {
    background-color: #f5f5f5;
  }

  #changelist table tbody td {
    padding: 8px;
    font-size: 13px;
    vertical-align: middle;
  }

  .paginator {
    padding: 10px;
    background: #f8f8f8;
    border-top: 1px solid #eee;
    text-align: right;
    color: #666;
    font-size: 13px;
  }

  .object-tools {
    float: right;
    margin-top: -32px;
    margin-right: 10px;
  }

  .object-tools a {
    display: inline-block;
    background: #79aec8;
    color: #fff;
    font-size: 12px;
    padding: 4px 10px;
    border-radius: 4px;
    text-decoration: none;
  }

  .object-tools a:hover {
    background: #609ab6;
  }

  .addlink {
    padding-left: 16px;
    background: url(data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTYiIGhlaWdodD0iMTYiIHZpZXdCb3g9IjAgMCAxNiAxNiIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHBhdGggZD0iTTggMFY4TTAgOEg0TTggOFYxNk04IDhIOCIgc3Ryb2tlPSJ3aGl0ZSIgc3Ryb2tlLXdpZHRoPSIyIi8+Cjwvc3ZnPgo=)
      0 0 no-repeat;
  }
</style>
{% endblock %} {% block content %}
<div id="content" class="container py-4">
  <div id="content-main">
    <div class="module" id="changelist">
      <h2>
        {% if entity_type == 'contractors' %} Registered Contractors {% elif
        entity_type == 'enterprises' %} Registered Enterprises {% elif
        entity_type == 'all' %} All Registered Entities {% endif %}
        <div class="object-tools">
          <a href="{% url 'vendors_registration' %}" class="addlink">Add</a>
        </div>
      </h2>

      <div class="changelist-actions">
        <span class="small">
          {{ total_count }} {% if entity_type == 'contractors' %} registered
          contractors {% elif entity_type == 'enterprises' %} registered
          enterprises {% elif entity_type == 'all' %} registered entities {%
          endif %}
        </span>

        <div class="filter-options mt-2">
          <div class="btn-group" role="group">
            <a
              href="?type=all"
              class="btn btn-sm {% if entity_type == 'all' %}btn-primary{% else %}btn-outline-primary{% endif %}"
              >All</a
            >
            <a
              href="?type=contractors"
              class="btn btn-sm {% if entity_type == 'contractors' %}btn-primary{% else %}btn-outline-primary{% endif %}"
              >Contractors</a
            >
            <a
              href="?type=enterprises"
              class="btn btn-sm {% if entity_type == 'enterprises' %}btn-primary{% else %}btn-outline-primary{% endif %}"
              >Enterprises</a
            >
          </div>
        </div>
      </div>

      <form id="changelist-form">
        <div class="results">
          <table id="result_list">
            <thead>
              <tr>
                <th scope="col">የተመዘገበው ስም</th>
                <th scope="col">የኮንትራክተሩ አይነት</th>
                <th scope="col">የመወዳደሪያ መለያ ቁጥር</th>
                <th scope="col">ተመዝግቧል?</th>
                <th scope="col">የመዝጋቢ ሰራተኛው ስም</th>
                <th scope="col">የምዝገባ ቀን</th>
              </tr>
            </thead>
            <tbody>
              {% for contractor in registered_contractors %}
              <tr class="{% cycle 'row1' 'row2' %}">
                <td>
                  <a
                    href="{% url 'vendor_detail' vendor_id=contractor.id %}"
                    class="link-primary"
                  >
                    {{ contractor.name|default:"Unnamed" }}
                  </a>
                </td>
                <td>
                  {% if contractor.contractor_type == 'contractor' %} Contractor
                  {% elif contractor.contractor_type == 'enterprise' %}
                  Enterprise {% else %} New Enterprise {% endif %}
                </td>
                <td>{{ contractor.identification_number|default:"N/A" }}</td>
                <td>True</td>
                <td>{{ contractor.registrar_name|default:"N/A" }}</td>
                <td>
                  {% if contractor.created_at %} {{
                  contractor.created_at|date:"M d, Y, g:i a" }} {% else %} N/A
                  {% endif %}
                </td>
              </tr>
              {% empty %}
              <tr>
                <td colspan="6" class="text-center py-4">
                  <p class="text-muted">No registered contractors found.</p>
                </td>
              </tr>
              {% endfor %}
            </tbody>
          </table>
        </div>

        <div class="paginator">{{ total_count }} registered contractors</div>
      </form>
    </div>
  </div>
</div>
{% endif%} {% endblock %}
