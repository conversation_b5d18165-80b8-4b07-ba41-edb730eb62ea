"""
Management command to update existing workload expected_duration based on quantity.
This ensures fair evaluation where larger projects get proportionally longer durations.

Usage:
    python manage.py update_workload_durations
    python manage.py update_workload_durations --dry-run  # Preview changes without applying
"""

from django.core.management.base import BaseCommand
from django.db import transaction
from eeucontractors.models import SubActivityWorkload


class Command(BaseCommand):
    help = 'Update existing workload expected_duration based on quantity (expected_duration = base_duration × quantity)'

    def add_arguments(self, parser):
        parser.add_argument(
            '--dry-run',
            action='store_true',
            help='Preview changes without applying them',
        )

    def handle(self, *args, **options):
        dry_run = options['dry_run']
        
        if dry_run:
            self.stdout.write(
                self.style.WARNING('🔍 DRY RUN MODE - No changes will be applied')
            )
        
        # Get all workloads that need updating
        workloads = SubActivityWorkload.objects.select_related('sub_activity').all()
        
        updated_count = 0
        error_count = 0
        
        self.stdout.write(f"📊 Found {workloads.count()} workloads to process...")
        
        with transaction.atomic():
            for workload in workloads:
                try:
                    # Calculate new expected_duration
                    if workload.sub_activity and workload.quantity:
                        old_duration = workload.expected_duration
                        new_duration = int(float(workload.sub_activity.base_duration) * workload.quantity)
                        
                        # Ensure minimum duration of 1 day
                        if new_duration < 1:
                            new_duration = 1
                        
                        if old_duration != new_duration:
                            self.stdout.write(
                                f"  📝 {workload.sub_activity.name} (Qty: {workload.quantity}): "
                                f"{old_duration} → {new_duration} days "
                                f"({workload.sub_activity.base_duration} × {workload.quantity})"
                            )
                            
                            if not dry_run:
                                workload.expected_duration = new_duration
                                workload.save()
                            
                            updated_count += 1
                        
                except Exception as e:
                    error_count += 1
                    self.stdout.write(
                        self.style.ERROR(
                            f"❌ Error updating workload {workload.id}: {e}"
                        )
                    )
        
        # Summary
        if dry_run:
            self.stdout.write(
                self.style.SUCCESS(
                    f"\n✅ DRY RUN COMPLETE:\n"
                    f"   - {updated_count} workloads would be updated\n"
                    f"   - {error_count} errors encountered\n"
                    f"   - Run without --dry-run to apply changes"
                )
            )
        else:
            self.stdout.write(
                self.style.SUCCESS(
                    f"\n✅ UPDATE COMPLETE:\n"
                    f"   - {updated_count} workloads updated\n"
                    f"   - {error_count} errors encountered\n"
                    f"   - Duration calculation: expected_duration = base_duration × quantity"
                )
            )
        
        if updated_count > 0:
            self.stdout.write(
                self.style.WARNING(
                    f"\n💡 IMPORTANT: Projects with larger quantities now have proportionally longer durations.\n"
                    f"   This ensures fair evaluation between small and large projects."
                )
            )
