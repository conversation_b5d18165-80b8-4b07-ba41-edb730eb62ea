from django.test import TestCase
from decimal import Decimal
from .models import (
    MainActivity, SubActivity, Project, ProjectProgress,
    SubActivityWorkload, Vendor, Vendorsregistration
)


class WorkloadDurationCalculationTest(TestCase):
    """Test the new duration calculation functionality"""

    def setUp(self):
        """Set up test data"""
        # Create vendor registration
        self.vendor_reg = Vendorsregistration.objects.create(
            contractor_name="Test Contractor",
            contractor_type="INDIVIDUAL",
            region="ADDIS_ABABA"
        )

        # Create vendor
        self.vendor = Vendor.objects.create(
            vendor_registration=self.vendor_reg
        )

        # Create main activity
        self.main_activity = MainActivity.objects.create(
            name="Test Main Activity",
            description="Test description"
        )

        # Create sub activity with base duration
        self.sub_activity = SubActivity.objects.create(
            main_activity=self.main_activity,
            name="Test Sub Activity",
            description="Test sub activity",
            base_duration=Decimal('5.0'),  # 5 days base duration
            order=1
        )

        # Create project
        self.project = Project.objects.create(
            project_name="Test Project",
            vendor=self.vendor,
            region="ADDIS_ABABA"
        )

        # Create project progress
        self.project_progress = ProjectProgress.objects.create(
            project=self.project,
            vendor=self.vendor,
            main_activity=self.main_activity
        )

    def test_workload_duration_calculation_on_save(self):
        """Test that expected_duration is calculated correctly when workload is saved"""
        # Test with quantity = 1
        workload1 = SubActivityWorkload.objects.create(
            project_progress=self.project_progress,
            sub_activity=self.sub_activity,
            quantity=1
        )
        self.assertEqual(workload1.expected_duration, 5)  # 5 * 1 = 5

        # Test with quantity = 10 (larger project)
        workload2 = SubActivityWorkload.objects.create(
            project_progress=self.project_progress,
            sub_activity=self.sub_activity,
            quantity=10
        )
        self.assertEqual(workload2.expected_duration, 50)  # 5 * 10 = 50

        # Test with quantity = 100 (very large project)
        workload3 = SubActivityWorkload.objects.create(
            project_progress=self.project_progress,
            sub_activity=self.sub_activity,
            quantity=100
        )
        self.assertEqual(workload3.expected_duration, 500)  # 5 * 100 = 500

    def test_workload_duration_update_on_quantity_change(self):
        """Test that expected_duration updates when quantity changes"""
        workload = SubActivityWorkload.objects.create(
            project_progress=self.project_progress,
            sub_activity=self.sub_activity,
            quantity=5
        )
        self.assertEqual(workload.expected_duration, 25)  # 5 * 5 = 25

        # Update quantity
        workload.quantity = 20
        workload.save()
        self.assertEqual(workload.expected_duration, 100)  # 5 * 20 = 100

    def test_auto_create_missing_workloads_with_quantity(self):
        """Test auto-create missing workloads with specified quantity"""
        # Create another sub activity
        sub_activity2 = SubActivity.objects.create(
            main_activity=self.main_activity,
            name="Test Sub Activity 2",
            description="Test sub activity 2",
            base_duration=Decimal('3.0'),  # 3 days base duration
            order=2
        )

        # Auto-create missing workloads with quantity 15
        created_count = self.project_progress.auto_create_missing_workloads(default_quantity=15)

        # Should create 2 workloads (for both sub activities)
        self.assertEqual(created_count, 2)

        # Check that durations are calculated correctly
        workloads = SubActivityWorkload.objects.filter(project_progress=self.project_progress)

        workload1 = workloads.get(sub_activity=self.sub_activity)
        self.assertEqual(workload1.quantity, 15)
        self.assertEqual(workload1.expected_duration, 75)  # 5 * 15 = 75

        workload2 = workloads.get(sub_activity=sub_activity2)
        self.assertEqual(workload2.quantity, 15)
        self.assertEqual(workload2.expected_duration, 45)  # 3 * 15 = 45
