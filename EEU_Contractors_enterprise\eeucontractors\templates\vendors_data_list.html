{% extends 'base.html' %}
{% load static %}

{% block title %}Vendors Data List{% endblock %}

{% block extra_css %}
<style>
  .data-list-container {
    margin-top: 20px;
  }
  
  .filter-bar {
    background-color: #f8f9fa;
    padding: 15px;
    border-radius: 5px;
    margin-bottom: 20px;
  }
  
  .data-card {
    border: 1px solid #ddd;
    border-radius: 5px;
    margin-bottom: 20px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
  }
  
  .data-card-header {
    background-color: #f8f9fa;
    padding: 10px 15px;
    border-bottom: 1px solid #ddd;
    display: flex;
    justify-content: space-between;
    align-items: center;
  }
  
  .data-card-body {
    padding: 15px;
  }
  
  .data-section {
    margin-bottom: 15px;
    padding-bottom: 15px;
    border-bottom: 1px solid #eee;
  }
  
  .data-section:last-child {
    border-bottom: none;
    margin-bottom: 0;
    padding-bottom: 0;
  }
  
  .section-title {
    font-weight: bold;
    margin-bottom: 10px;
    color: #495057;
  }
  
  .data-row {
    display: flex;
    margin-bottom: 5px;
  }
  
  .data-label {
    flex: 0 0 40%;
    font-weight: 500;
  }
  
  .data-value {
    flex: 0 0 60%;
  }
  
  .status-badge {
    padding: 5px 10px;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: bold;
  }
  
  .status-complete {
    background-color: #d4edda;
    color: #155724;
  }
  
  .status-partial {
    background-color: #fff3cd;
    color: #856404;
  }
  
  .status-incomplete {
    background-color: #f8d7da;
    color: #721c24;
  }
  
  .progress {
    height: 10px;
    margin-top: 5px;
  }
</style>
{% endblock %}

{% block content %}
<div class="container data-list-container">
  <h2 class="mb-4">የተቋራጮች መረጃ ዝርዝር</h2>
  
  <!-- Filter Bar -->
  <div class="filter-bar">
    <form method="get" class="row g-3">
      <div class="col-md-4">
        <label for="contractor_type" class="form-label">የኮንትራክተሩ አይነት</label>
        <select name="contractor_type" id="contractor_type" class="form-select" onchange="this.form.submit()">
          <option value="" {% if selected_type ='all' %}selected{% endif %}>ሁሉም</option>
          <option value="contractor" {% if selected_type ='contractor' %}selected{% endif %}>Contractor</option>
          <option value="enterprise" {% if selected_type ='enterprise' %}selected{% endif %}>Enterprise</option>
          <option value="new_enterprise" {% if selected_type ='new_enterprise' %}selected{% endif %}>New Enterprise</option>
        </select>
      </div>
    </form>
  </div>
  
  <!-- Data Cards -->
  {% for data in vendors_data %}
  <div class="data-card">
    <div class="data-card-header">
      <h5 class="mb-0">{{ data.contractor_name_display }}</h5>
      <span class="status-badge 
        {% if data.overall_completion_percentage >= 75 %}status-complete
        {% elif data.overall_completion_percentage >= 50 %}status-partial
        {% else %}status-incomplete{% endif %}">
        {{ data.overall_completion_status }} ({{ data.overall_completion_percentage }}%)
      </span>
    </div>
    <div class="data-card-body">
      <!-- Basic Information Section -->
      <div class="data-section">
        <div class="section-title">የተቋራጭ መረጃ</div>
        <div class="data-row">
          <div class="data-label">የኮንትራክተሩ አይነት:</div>
          <div class="data-value">{{ data.contractor_type_display }}</div>
        </div>
        <div class="data-row">
          <div class="data-label">የመወዳደሪያ መለያ ቁጥር:</div>
          <div class="data-value">{{ data.identification_number_display }}</div>
        </div>
      </div>
      
      <!-- Human Resources Section -->
      <div class="data-section">
        <div class="section-title">የሰው ኃይል መረጃ</div>
        <div class="data-row">
          <div class="data-label">የሙያ ብቃት:</div>
          <div class="data-value">{{ data.professional_license_status }}</div>
        </div>
        <div class="data-row">
          <div class="data-label">የኤሌክትሪክ ስራ ተቋራጭ ደረጃ:</div>
          <div class="data-value">{{ data.electric_grade_display }}</div>
        </div>
        <div class="data-row">
          <div class="data-label">የሰው ኃይል:</div>
          <div class="data-value">{{ data.human_resource_status }}</div>
        </div>
        <div class="data-row">
          <div class="data-label">ጠቅላላ ሰራተኞች:</div>
          <div class="data-value">{{ data.total_staff }} (ምህንድሶች: {{ data.total_engineers }}, ቴክኒሻኖች: {{ data.total_technicians }}, የመስመር ሰራተኞች: {{ data.vendor.line_workers }})</div>
        </div>
      </div>
      
      <!-- Documents Section -->
      <div class="data-section">
        <div class="section-title">የሰነድ ቼክሊስት</div>
        <div class="data-row">
          <div class="data-label">የቀረቡ ሰነዶች:</div>
          <div class="data-value">{{ data.documents_provided_count }} / {{ data.total_documents_required }}</div>
        </div>
        <div class="progress">
          <div class="progress-bar" role="progressbar" style="width: {{ data.documents_completion_percentage }}%" 
               aria-valuenow="{{ data.documents_completion_percentage }}" aria-valuemin="0" aria-valuemax="100"></div>
        </div>
      </div>
      
      <!-- Registration Section -->
      <div class="data-section">
        <div class="section-title">የምዝገባ ዝርዝሮች</div>
        <div class="data-row">
          <div class="data-label">የምዝገባ ሁኔታ:</div>
          <div class="data-value">{{ data.registration_status }}</div>
        </div>
        <div class="data-row">
          <div class="data-label">የመዝጋቢ መረጃ:</div>
          <div class="data-value">{{ data.registrar_info }}</div>
        </div>
        <div class="data-row">
          <div class="data-label">የምዝገባ ቀን:</div>
          <div class="data-value">{{ data.registration_date }}</div>
        </div>
      </div>
      
      <!-- Action Buttons -->
      <div class="mt-3">
        <a href="{% url 'vendor_detail' vendor_id=data.vendor.id %}" class="btn btn-primary btn-sm">
          <i class="bi bi-eye"></i> ሙሉ መረጃ ይመልከቱ
        </a>
      </div>
    </div>
  </div>
  {% empty %}
  <div class="alert alert-info">
    No vendors found matching the selected criteria.
  </div>
  {% endfor %}
</div>
{% endblock %}